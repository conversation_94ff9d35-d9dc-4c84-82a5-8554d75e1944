# API测试和调试指南

## 📋 概述

本指南为前端开发者提供完整的API测试和调试方法，帮助您快速验证API功能和排查问题。

## 🛠️ 测试工具

### 1. Postman集合

#### 导入Postman集合
```json
{
  "info": {
    "name": "LangGraph智能体API",
    "description": "完整的API测试集合",
    "version": "1.0.0"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8000/api",
      "type": "string"
    },
    {
      "key": "token",
      "value": "",
      "type": "string"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{token}}",
        "type": "string"
      }
    ]
  },
  "item": [
    {
      "name": "1. 认证测试",
      "item": [
        {
          "name": "登录",
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "if (pm.response.code === 200) {",
                  "    const response = pm.response.json();",
                  "    if (response.success && response.data.access_token) {",
                  "        pm.collectionVariables.set('token', response.data.access_token);",
                  "        pm.test('登录成功', () => {",
                  "            pm.expect(response.success).to.be.true;",
                  "        });",
                  "    }",
                  "}"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"demo\",\n  \"password\": \"demo123\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/auth/login",
              "host": ["{{baseUrl}}"],
              "path": ["auth", "login"]
            }
          }
        },
        {
          "name": "获取用户信息",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/auth/me",
              "host": ["{{baseUrl}}"],
              "path": ["auth", "me"]
            }
          }
        }
      ]
    },
    {
      "name": "2. 会话管理",
      "item": [
        {
          "name": "获取会话列表",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/sessions?page=1&per_page=10",
              "host": ["{{baseUrl}}"],
              "path": ["sessions"],
              "query": [
                {
                  "key": "page",
                  "value": "1"
                },
                {
                  "key": "per_page",
                  "value": "10"
                }
              ]
            }
          }
        },
        {
          "name": "创建新会话",
          "event": [
            {
              "listen": "test",
              "script": {
                "exec": [
                  "if (pm.response.code === 200) {",
                  "    const response = pm.response.json();",
                  "    if (response.success && response.data.session) {",
                  "        pm.collectionVariables.set('sessionId', response.data.session.id);",
                  "    }",
                  "}"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"title\": \"测试会话\",\n  \"mode\": \"multi_agent\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/sessions",
              "host": ["{{baseUrl}}"],
              "path": ["sessions"]
            }
          }
        }
      ]
    },
    {
      "name": "3. 消息处理",
      "item": [
        {
          "name": "发送消息",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"content\": \"请帮我分析一下人工智能的发展趋势\",\n  \"type\": \"text\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/sessions/{{sessionId}}/messages",
              "host": ["{{baseUrl}}"],
              "path": ["sessions", "{{sessionId}}", "messages"]
            }
          }
        },
        {
          "name": "获取消息历史",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/sessions/{{sessionId}}/messages?page=1&per_page=20",
              "host": ["{{baseUrl}}"],
              "path": ["sessions", "{{sessionId}}", "messages"],
              "query": [
                {
                  "key": "page",
                  "value": "1"
                },
                {
                  "key": "per_page",
                  "value": "20"
                }
              ]
            }
          }
        }
      ]
    },
    {
      "name": "4. 系统状态",
      "item": [
        {
          "name": "获取智能体状态",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/system/agents",
              "host": ["{{baseUrl}}"],
              "path": ["system", "agents"]
            }
          }
        },
        {
          "name": "系统健康检查",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{baseUrl}}/../health",
              "host": ["{{baseUrl}}"],
              "path": ["..", "health"]
            }
          }
        }
      ]
    }
  ]
}
```

### 2. cURL命令示例

#### 认证测试
```bash
# 登录
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "demo",
    "password": "demo123"
  }'

# 获取用户信息
curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 会话管理
```bash
# 创建会话
curl -X POST "http://localhost:8000/api/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "title": "测试会话",
    "mode": "multi_agent"
  }'

# 获取会话列表
curl -X GET "http://localhost:8000/api/sessions?page=1&per_page=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 消息处理
```bash
# 发送消息
curl -X POST "http://localhost:8000/api/sessions/SESSION_ID/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "content": "请帮我分析一下人工智能的发展趋势",
    "type": "text"
  }'
```

## 🔍 WebSocket测试

### 1. 浏览器控制台测试
```javascript
// 在浏览器控制台中测试WebSocket连接
const token = localStorage.getItem('access_token');
const sessionId = 'your_session_id';
const ws = new WebSocket(`ws://localhost:8000/ws/sessions/${sessionId}?token=${token}`);

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};

// 发送测试消息
ws.send(JSON.stringify({
    type: 'user_message',
    content: '你好，这是一个测试消息'
}));
```

### 2. Node.js WebSocket测试脚本
```javascript
// websocket-test.js
const WebSocket = require('ws');

const token = 'YOUR_TOKEN_HERE';
const sessionId = 'your_session_id';
const wsUrl = `ws://localhost:8000/ws/sessions/${sessionId}?token=${token}`;

const ws = new WebSocket(wsUrl);

ws.on('open', function open() {
    console.log('WebSocket连接已建立');
    
    // 发送测试消息
    ws.send(JSON.stringify({
        type: 'user_message',
        content: '请帮我分析一下人工智能的发展趋势'
    }));
});

ws.on('message', function message(data) {
    const parsed = JSON.parse(data);
    console.log('收到消息:', JSON.stringify(parsed, null, 2));
});

ws.on('error', function error(err) {
    console.error('WebSocket错误:', err);
});

ws.on('close', function close() {
    console.log('WebSocket连接已关闭');
});

// 运行: node websocket-test.js
```

## 🐛 常见问题调试

### 1. 认证问题

#### 问题：401 Unauthorized
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "需要认证"
  }
}
```

**解决方案**：
1. 检查token是否正确设置
2. 验证token是否过期
3. 确认Authorization头格式：`Bearer YOUR_TOKEN`

#### 调试代码
```javascript
// 检查token
const token = localStorage.getItem('access_token');
console.log('当前token:', token);

// 解析JWT token（仅用于调试）
function parseJWT(token) {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        return JSON.parse(jsonPayload);
    } catch (error) {
        console.error('解析token失败:', error);
        return null;
    }
}

const payload = parseJWT(token);
console.log('Token payload:', payload);
console.log('Token过期时间:', new Date(payload.exp * 1000));
```

### 2. CORS问题

#### 问题：跨域请求被阻止
```
Access to fetch at 'http://localhost:8000/api/sessions' from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解决方案**：
1. 确认后端CORS配置正确
2. 检查请求头设置
3. 验证预检请求处理

#### 调试代码
```javascript
// 检查CORS预检请求
fetch('http://localhost:8000/api/sessions', {
    method: 'OPTIONS',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
})
.then(response => {
    console.log('CORS预检响应:', response.headers);
})
.catch(error => {
    console.error('CORS预检失败:', error);
});
```

### 3. WebSocket连接问题

#### 问题：WebSocket连接失败
```
WebSocket connection to 'ws://localhost:8000/ws/sessions/xxx' failed
```

**解决方案**：
1. 检查WebSocket URL格式
2. 验证token参数
3. 确认后端WebSocket服务运行

#### 调试代码
```javascript
// WebSocket连接调试
function debugWebSocket(sessionId, token) {
    const wsUrl = `ws://localhost:8000/ws/sessions/${sessionId}?token=${token}`;
    console.log('尝试连接WebSocket:', wsUrl);
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = function(event) {
        console.log('✅ WebSocket连接成功');
        console.log('连接事件:', event);
    };
    
    ws.onerror = function(error) {
        console.error('❌ WebSocket连接错误:', error);
        console.log('错误详情:', {
            readyState: ws.readyState,
            url: ws.url,
            protocol: ws.protocol
        });
    };
    
    ws.onclose = function(event) {
        console.log('🔌 WebSocket连接关闭');
        console.log('关闭详情:', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean
        });
    };
    
    return ws;
}
```

### 4. API响应格式问题

#### 问题：响应数据格式不符合预期
```javascript
// 响应验证函数
function validateApiResponse(response, expectedFields = []) {
    console.log('API响应:', response);
    
    // 检查基本结构
    if (!response.hasOwnProperty('success')) {
        console.error('❌ 响应缺少success字段');
        return false;
    }
    
    if (response.success) {
        if (!response.data) {
            console.error('❌ 成功响应缺少data字段');
            return false;
        }
        
        // 检查期望字段
        expectedFields.forEach(field => {
            if (!response.data.hasOwnProperty(field)) {
                console.error(`❌ 响应数据缺少字段: ${field}`);
            }
        });
    } else {
        if (!response.error) {
            console.error('❌ 错误响应缺少error字段');
            return false;
        }
    }
    
    console.log('✅ 响应格式验证通过');
    return true;
}

// 使用示例
fetch('/api/sessions')
    .then(response => response.json())
    .then(data => {
        validateApiResponse(data, ['sessions', 'total', 'page']);
    });
```

## 📊 性能测试

### 1. API响应时间测试
```javascript
// 性能测试函数
async function performanceTest(apiCall, iterations = 10) {
    const results = [];
    
    for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        try {
            await apiCall();
            const endTime = performance.now();
            results.push(endTime - startTime);
        } catch (error) {
            console.error(`第${i+1}次请求失败:`, error);
        }
    }
    
    const avgTime = results.reduce((a, b) => a + b, 0) / results.length;
    const minTime = Math.min(...results);
    const maxTime = Math.max(...results);
    
    console.log('性能测试结果:');
    console.log(`平均响应时间: ${avgTime.toFixed(2)}ms`);
    console.log(`最快响应时间: ${minTime.toFixed(2)}ms`);
    console.log(`最慢响应时间: ${maxTime.toFixed(2)}ms`);
    console.log(`成功率: ${(results.length / iterations * 100).toFixed(1)}%`);
}

// 测试会话列表API
performanceTest(async () => {
    const response = await fetch('/api/sessions', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
});
```

### 2. WebSocket消息延迟测试
```javascript
// WebSocket延迟测试
function websocketLatencyTest(ws, messageCount = 10) {
    let sentCount = 0;
    let receivedCount = 0;
    const latencies = [];
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        if (data.type === 'test_response') {
            const latency = Date.now() - data.timestamp;
            latencies.push(latency);
            receivedCount++;
            
            if (receivedCount === messageCount) {
                const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
                console.log(`WebSocket平均延迟: ${avgLatency.toFixed(2)}ms`);
            }
        }
    };
    
    // 发送测试消息
    const interval = setInterval(() => {
        if (sentCount < messageCount) {
            ws.send(JSON.stringify({
                type: 'test_message',
                timestamp: Date.now()
            }));
            sentCount++;
        } else {
            clearInterval(interval);
        }
    }, 1000);
}
```

## 📝 测试检查清单

### API功能测试
- [ ] 用户登录/登出
- [ ] 获取用户信息
- [ ] 创建/删除会话
- [ ] 发送/接收消息
- [ ] 获取智能体状态
- [ ] 文件上传/下载

### WebSocket测试
- [ ] 连接建立
- [ ] 消息发送/接收
- [ ] 连接断开重连
- [ ] 错误处理

### 错误处理测试
- [ ] 无效token处理
- [ ] 网络错误处理
- [ ] 服务器错误处理
- [ ] 参数验证错误

### 性能测试
- [ ] API响应时间
- [ ] WebSocket延迟
- [ ] 并发请求处理
- [ ] 内存使用情况

通过这个完整的测试和调试指南，您可以系统地验证API功能并快速定位问题！
