# 前端开发快速开始指南

## 🎯 项目概述

本指南帮助前端开发小白快速搭建LangGraph智能体系统的前端界面。我们将使用React + TypeScript构建一个现代化的聊天界面。

## 📋 技术栈

- **前端框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design / Material-UI
- **HTTP客户端**: Axios
- **WebSocket**: 原生WebSocket API
- **构建工具**: Vite
- **样式**: CSS Modules / Styled Components

## 🚀 项目初始化

### 1. 创建React项目
```bash
# 使用Vite创建React + TypeScript项目
npm create vite@latest langgraph-frontend -- --template react-ts
cd langgraph-frontend

# 安装依赖
npm install

# 安装额外依赖
npm install @reduxjs/toolkit react-redux axios antd @ant-design/icons
npm install -D @types/node
```

### 2. 项目结构
```
src/
├── components/          # 可复用组件
│   ├── Chat/           # 聊天相关组件
│   ├── Layout/         # 布局组件
│   └── Common/         # 通用组件
├── pages/              # 页面组件
│   ├── Login/          # 登录页面
│   ├── Chat/           # 聊天页面
│   └── Dashboard/      # 仪表板
├── store/              # Redux状态管理
│   ├── slices/         # Redux切片
│   └── index.ts        # Store配置
├── services/           # API服务
│   ├── api.ts          # API客户端
│   ├── auth.ts         # 认证服务
│   └── websocket.ts    # WebSocket服务
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── hooks/              # 自定义Hooks
└── styles/             # 样式文件
```

## 🔧 核心配置

### 1. API客户端配置 (src/services/api.ts)
```typescript
import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

class ApiClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    const response = await this.instance.get(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.instance.post(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.instance.put(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.instance.delete(url);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### 2. TypeScript类型定义 (src/types/index.ts)
```typescript
export interface User {
  id: string;
  username: string;
  created_at: string;
  sessions_count?: number;
  last_login?: string;
}

export interface Session {
  id: string;
  title: string;
  mode: 'single_agent' | 'multi_agent';
  created_at: string;
  updated_at: string;
  message_count: number;
  status: 'active' | 'archived';
}

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  agent_info?: {
    agent_name: string;
    tools_used: string[];
    execution_time?: number;
  };
  isStreaming?: boolean;
}

export interface Agent {
  name: string;
  status: 'active' | 'working' | 'idle' | 'error';
  description: string;
  current_task?: string;
  progress?: number;
}

export interface WebSocketMessage {
  type: 'message_chunk' | 'agent_status' | 'message_complete' | 'error';
  message_id?: string;
  content?: string;
  is_final?: boolean;
  agent?: string;
  status?: string;
  message?: Message;
  error?: string;
}
```

### 3. Redux Store配置 (src/store/index.ts)
```typescript
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import chatSlice from './slices/chatSlice';
import agentSlice from './slices/agentSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    chat: chatSlice,
    agent: agentSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### 4. 认证Slice (src/store/slices/authSlice.ts)
```typescript
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiClient } from '../../services/api';
import { User } from '../../types';

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('access_token'),
  isLoading: false,
  error: null,
};

export const login = createAsyncThunk(
  'auth/login',
  async ({ username, password }: { username: string; password: string }) => {
    const response = await apiClient.post('/auth/login', { username, password });
    if (response.success && response.data) {
      localStorage.setItem('access_token', response.data.access_token);
      return response.data;
    }
    throw new Error(response.error?.message || '登录失败');
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async () => {
    const response = await apiClient.get<User>('/auth/me');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.error?.message || '获取用户信息失败');
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      localStorage.removeItem('access_token');
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.access_token;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '登录失败';
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.user = action.payload;
      });
  },
});

export const { logout, clearError } = authSlice.actions;
export default authSlice.reducer;
```

## 🎨 核心组件

### 1. 登录组件 (src/components/Login/LoginForm.tsx)
```tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { login } from '../../store/slices/authSlice';

const LoginForm: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const onFinish = async (values: { username: string; password: string }) => {
    try {
      await dispatch(login(values)).unwrap();
      message.success('登录成功');
    } catch (error) {
      message.error(error as string);
    }
  };

  return (
    <div className="login-container">
      <Card title="LangGraph 智能体系统" className="login-card">
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className="demo-info">
          <p>演示账号：demo / demo123</p>
        </div>
      </Card>
    </div>
  );
};

export default LoginForm;
```

### 2. 聊天界面组件 (src/components/Chat/ChatInterface.tsx)
```tsx
import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Card, Avatar, Tag, Progress } from 'antd';
import { SendOutlined, RobotOutlined, UserOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { Message, Agent } from '../../types';
import { useWebSocket } from '../../hooks/useWebSocket';

const { TextArea } = Input;

interface ChatInterfaceProps {
  sessionId: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ sessionId }) => {
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const { agents } = useAppSelector((state) => state.agent);
  const { sendMessage, isConnected } = useWebSocket(sessionId);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSend = () => {
    if (!inputValue.trim() || !isConnected) return;

    const userMessage: Message = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    sendMessage(inputValue);
    setInputValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'working': return 'orange';
      case 'idle': return 'default';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  return (
    <div className="chat-interface">
      {/* 智能体状态栏 */}
      <Card className="agent-status-bar" size="small">
        <div className="agent-list">
          {agents.map((agent: Agent) => (
            <div key={agent.name} className="agent-item">
              <Tag color={getAgentStatusColor(agent.status)}>
                {agent.name}
              </Tag>
              {agent.progress && (
                <Progress
                  percent={agent.progress}
                  size="small"
                  showInfo={false}
                />
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* 消息列表 */}
      <div className="messages-container">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="message-header">
              <Avatar
                icon={message.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                size="small"
              />
              <span className="timestamp">
                {new Date(message.timestamp).toLocaleTimeString()}
              </span>
              {message.agent_info && (
                <Tag size="small">{message.agent_info.agent_name}</Tag>
              )}
            </div>
            <div className="message-content">
              {message.content}
              {message.isStreaming && <span className="cursor">|</span>}
            </div>
            {message.agent_info?.tools_used && (
              <div className="tools-used">
                工具: {message.agent_info.tools_used.join(', ')}
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="input-container">
        <TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="输入您的问题..."
          autoSize={{ minRows: 2, maxRows: 4 }}
          disabled={!isConnected}
        />
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleSend}
          disabled={!inputValue.trim() || !isConnected}
        >
          发送
        </Button>
      </div>
    </div>
  );
};

export default ChatInterface;
```

### 3. WebSocket Hook (src/hooks/useWebSocket.ts)
```typescript
import { useEffect, useRef, useCallback } from 'react';
import { useAppDispatch } from './redux';
import { updateAgentStatus } from '../store/slices/agentSlice';
import { WebSocketMessage } from '../types';

export const useWebSocket = (sessionId: string) => {
  const wsRef = useRef<WebSocket | null>(null);
  const dispatch = useAppDispatch();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    const token = localStorage.getItem('access_token');
    const wsUrl = `${import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000/ws'}/sessions/${sessionId}?token=${token}`;
    
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      console.log('WebSocket连接已建立');
      reconnectAttempts.current = 0;
    };

    wsRef.current.onmessage = (event) => {
      try {
        const data: WebSocketMessage = JSON.parse(event.data);
        handleMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    wsRef.current.onclose = () => {
      console.log('WebSocket连接已关闭');
      attemptReconnect();
    };

    wsRef.current.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }, [sessionId]);

  const handleMessage = (data: WebSocketMessage) => {
    switch (data.type) {
      case 'agent_status':
        if (data.agent && data.status) {
          dispatch(updateAgentStatus({
            agent: data.agent,
            status: data.status as any
          }));
        }
        break;
      // 其他消息类型处理...
    }
  };

  const attemptReconnect = () => {
    if (reconnectAttempts.current < maxReconnectAttempts) {
      reconnectAttempts.current++;
      reconnectTimeoutRef.current = setTimeout(() => {
        console.log(`尝试重连 (${reconnectAttempts.current}/${maxReconnectAttempts})`);
        connect();
      }, 1000 * reconnectAttempts.current);
    }
  };

  const sendMessage = useCallback((content: string) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'user_message',
        content
      }));
    }
  }, []);

  useEffect(() => {
    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connect]);

  return {
    sendMessage,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN
  };
};
```

## 🎨 样式配置

### 1. 全局样式 (src/styles/global.css)
```css
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.agent-status-bar {
  margin-bottom: 16px;
}

.agent-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.agent-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: white;
  margin-bottom: 16px;
  border-radius: 8px;
}

.message {
  margin-bottom: 16px;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-content {
  padding: 12px;
  border-radius: 8px;
  line-height: 1.5;
}

.message.user .message-content {
  background: #1890ff;
  color: white;
}

.message.assistant .message-content {
  background: #f0f0f0;
  color: #333;
}

.cursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.tools-used {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.demo-info {
  text-align: center;
  margin-top: 16px;
  color: #666;
  font-size: 12px;
}
```

## 🚀 启动项目

### 1. 环境变量配置 (.env)
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_BASE_URL=ws://localhost:8000/ws
```

### 2. 启动命令
```bash
# 开发环境
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📝 开发建议

1. **组件拆分**: 将复杂组件拆分为更小的可复用组件
2. **类型安全**: 充分利用TypeScript的类型检查
3. **错误处理**: 实现完善的错误边界和用户友好的错误提示
4. **性能优化**: 使用React.memo、useMemo等优化渲染性能
5. **测试**: 编写单元测试和集成测试确保代码质量

这个快速开始指南提供了一个完整的前端开发框架，您可以在此基础上继续扩展功能！
