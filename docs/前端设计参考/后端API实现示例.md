# 后端API实现示例

## 📋 概述

本文档提供了基于FastAPI的后端API实现示例，帮助理解API的具体工作原理。

## 🚀 FastAPI项目结构

```
backend/
├── main.py                 # FastAPI应用入口
├── api/                    # API路由
│   ├── __init__.py
│   ├── auth.py            # 认证相关API
│   ├── sessions.py        # 会话管理API
│   ├── messages.py        # 消息处理API
│   ├── system.py          # 系统状态API
│   └── files.py           # 文件管理API
├── models/                # 数据模型
│   ├── __init__.py
│   ├── user.py
│   ├── session.py
│   └── message.py
├── services/              # 业务逻辑
│   ├── __init__.py
│   ├── auth_service.py
│   ├── chat_service.py
│   └── agent_service.py
├── core/                  # 核心配置
│   ├── __init__.py
│   ├── config.py
│   ├── security.py
│   └── database.py
└── requirements.txt
```

## 🔧 主要实现文件

### 1. main.py - FastAPI应用入口
```python
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

from api import auth, sessions, messages, system, files
from core.config import settings
from services.websocket_manager import WebSocketManager

app = FastAPI(
    title="LangGraph智能体API",
    description="多智能体协作系统API",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# API路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(sessions.router, prefix="/api/sessions", tags=["会话"])
app.include_router(messages.router, prefix="/api/messages", tags=["消息"])
app.include_router(system.router, prefix="/api/system", tags=["系统"])
app.include_router(files.router, prefix="/api/files", tags=["文件"])

# WebSocket管理器
websocket_manager = WebSocketManager()

@app.websocket("/ws/sessions/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket_manager.connect(websocket, session_id)
    try:
        while True:
            data = await websocket.receive_text()
            await websocket_manager.handle_message(session_id, data)
    except WebSocketDisconnect:
        websocket_manager.disconnect(session_id)

@app.get("/")
async def root():
    return {"message": "LangGraph智能体API服务"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
```

### 2. api/auth.py - 认证API
```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel
from datetime import datetime, timedelta
import jwt

from core.config import settings
from core.security import verify_password, create_access_token
from models.user import User

router = APIRouter()
security = HTTPBearer()

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: dict

class UserResponse(BaseModel):
    id: str
    username: str
    created_at: datetime
    sessions_count: int = 0
    last_login: datetime = None

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    # 验证用户凭据（这里简化处理）
    if request.username == "demo" and request.password == "demo123":
        user_data = {
            "id": "user_demo",
            "username": "demo",
            "created_at": datetime.now().isoformat()
        }
        
        # 创建访问令牌
        access_token = create_access_token(
            data={"sub": user_data["id"], "username": user_data["username"]}
        )
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_data
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户信息"""
    try:
        # 验证JWT令牌
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        user_id = payload.get("sub")
        username = payload.get("username")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌"
            )
        
        # 返回用户信息（这里简化处理）
        return UserResponse(
            id=user_id,
            username=username,
            created_at=datetime.now(),
            sessions_count=5,
            last_login=datetime.now()
        )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌"
        )

@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    # 在实际应用中，这里可以将令牌加入黑名单
    return {"message": "登出成功"}
```

### 3. api/sessions.py - 会话管理API
```python
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import uuid

from core.security import get_current_user
from models.session import Session

router = APIRouter()

class CreateSessionRequest(BaseModel):
    title: str
    mode: str = "multi_agent"

class SessionResponse(BaseModel):
    id: str
    title: str
    mode: str
    created_at: datetime
    updated_at: datetime
    message_count: int
    status: str

class SessionListResponse(BaseModel):
    sessions: List[SessionResponse]
    total: int
    page: int
    per_page: int

@router.get("", response_model=SessionListResponse)
async def get_sessions(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """获取用户会话列表"""
    # 模拟数据
    sessions = [
        SessionResponse(
            id=f"session_{i}",
            title=f"对话 {i}",
            mode="multi_agent",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            message_count=i * 2,
            status="active"
        )
        for i in range(1, 6)
    ]
    
    return SessionListResponse(
        sessions=sessions,
        total=len(sessions),
        page=page,
        per_page=per_page
    )

@router.post("", response_model=dict)
async def create_session(
    request: CreateSessionRequest,
    current_user: dict = Depends(get_current_user)
):
    """创建新会话"""
    session_id = f"session_{uuid.uuid4().hex[:8]}"
    
    session = SessionResponse(
        id=session_id,
        title=request.title,
        mode=request.mode,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        message_count=0,
        status="active"
    )
    
    return {
        "success": True,
        "data": {"session": session}
    }

@router.get("/{session_id}", response_model=dict)
async def get_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取会话详情"""
    session = SessionResponse(
        id=session_id,
        title="示例会话",
        mode="multi_agent",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        message_count=10,
        status="active"
    )
    
    return {
        "success": True,
        "data": {"session": session}
    }

@router.delete("/{session_id}")
async def delete_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """删除会话"""
    return {
        "success": True,
        "message": "会话删除成功"
    }

@router.post("/{session_id}/clear")
async def clear_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """清除会话历史"""
    return {
        "success": True,
        "message": "会话历史清除成功"
    }
```

### 4. api/messages.py - 消息处理API
```python
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from core.security import get_current_user
from services.chat_service import ChatService

router = APIRouter()

class SendMessageRequest(BaseModel):
    content: str
    type: str = "text"

class MessageResponse(BaseModel):
    id: str
    role: str
    content: str
    timestamp: datetime
    agent_info: Optional[Dict[str, Any]] = None

class MessageListResponse(BaseModel):
    messages: List[MessageResponse]
    total: int
    page: int
    per_page: int

@router.post("/sessions/{session_id}/messages")
async def send_message(
    session_id: str,
    request: SendMessageRequest,
    current_user: dict = Depends(get_current_user)
):
    """发送消息"""
    message_id = f"msg_{uuid.uuid4().hex[:8]}"
    
    # 这里会调用LangGraph智能体系统
    chat_service = ChatService()
    await chat_service.process_message(session_id, request.content, message_id)
    
    return {
        "success": True,
        "data": {
            "message_id": message_id,
            "session_id": session_id,
            "status": "processing"
        }
    }

@router.get("/sessions/{session_id}/messages", response_model=MessageListResponse)
async def get_messages(
    session_id: str,
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """获取消息历史"""
    # 模拟消息数据
    messages = [
        MessageResponse(
            id="msg_001",
            role="user",
            content="请帮我分析一下人工智能的发展趋势",
            timestamp=datetime.now()
        ),
        MessageResponse(
            id="msg_002",
            role="assistant",
            content="我来为您分析人工智能的发展趋势...",
            timestamp=datetime.now(),
            agent_info={
                "agent_name": "search_specialist",
                "tools_used": ["tavily_search", "web-search"],
                "execution_time": 2.5
            }
        )
    ]
    
    return MessageListResponse(
        messages=messages,
        total=len(messages),
        page=page,
        per_page=per_page
    )
```

### 5. services/websocket_manager.py - WebSocket管理
```python
from fastapi import WebSocket
from typing import Dict, List
import json
import asyncio

from services.chat_service import ChatService

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.chat_service = ChatService()

    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket

    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]

    async def send_message(self, session_id: str, message: dict):
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            await websocket.send_text(json.dumps(message))

    async def handle_message(self, session_id: str, data: str):
        try:
            message_data = json.loads(data)
            
            if message_data.get("type") == "user_message":
                content = message_data.get("content")
                
                # 处理用户消息
                async for chunk in self.chat_service.process_message_stream(session_id, content):
                    await self.send_message(session_id, chunk)
                    
        except Exception as e:
            await self.send_message(session_id, {
                "type": "error",
                "error": str(e)
            })
```

## 🔧 配置文件

### core/config.py
```python
from pydantic import BaseSettings
from typing import List

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "LangGraph智能体API"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24  # 24小时
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./agent_data.db"
    
    # LangGraph配置
    LANGGRAPH_CONFIG_PATH: str = "./config/langgraph_config.json"
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### requirements.txt
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.13.0
websockets==12.0
aiofiles==23.2.1
```

## 🚀 启动命令

### 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 或者使用Python直接运行
python main.py
```

### 生产环境
```bash
# 使用Gunicorn启动
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 📝 API文档访问

启动服务后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

这些文档会自动生成，包含所有API端点的详细信息、请求/响应模型和交互式测试界面。
