from typing import Optional

import chainlit as cl

starters = [
    cl.Starter(
        label="Say hi",
        message="Start a conversation with a greeting",
        icon="https://picsum.photos/300",
    ),
    cl.Starter(
        label="Ask for help",
        message="Ask for help with something",
        icon="https://picsum.photos/350",
    ),
]


@cl.set_chat_profiles
async def chat_profile(current_user: cl.User):
    if current_user.metadata["role"] != "ADMIN":
        return None

    return [
        cl.ChatProfile(
            name="GPT-3.5",
            icon="https://picsum.photos/250",
            markdown_description="The underlying LLM model is **GPT-3.5**, a *175B parameter model* trained on 410GB of text data.",
            starters=starters,
        ),
        cl.<PERSON>tProfile(
            name="GPT-4",
            markdown_description="The underlying LLM model is **GPT-4**, a *1.5T parameter model* trained on 3.5TB of text data. [Learn more](https://example.com/gpt4)",
            icon="https://picsum.photos/250",
            starters=starters,
        ),
        cl.<PERSON><PERSON><PERSON><PERSON><PERSON>le(
            name="GPT-5",
            markdown_description="The underlying LLM model is **GPT-5**.",
            icon="https://picsum.photos/200",
            starters=starters,
        ),
    ]


@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[cl.User]:
    if (username, password) == ("admin", "admin"):
        return cl.User(identifier="admin", metadata={"role": "ADMIN"})
    else:
        return None


@cl.on_message
async def on_message():
    user = cl.user_session.get("user")
    chat_profile = cl.user_session.get("chat_profile")
    await cl.Message(
        content=f"starting chat with {user.identifier} using the {chat_profile} chat profile"
    ).send()
