[tool.poetry]
name = "chainlit"
version = "2.5.5"
keywords = [
    'LLM',
    'Agents',
    'MCP',
    'gen ai',
    'chat ui',
    'chatbot ui',
    'openai',
    'copilot',
    'langchain',
    'conversational ai',
]
description = "Build Conversational AI."
authors = ["<PERSON>", "<PERSON>"]
license = "Apache-2.0"
homepage = "https://chainlit.io/"
documentation = "https://docs.chainlit.io/"
classifiers = [
    "Framework :: FastAPI",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Communications :: Chat",
    "Programming Language :: JavaScript",
    "Topic :: Software Development :: User Interfaces",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Environment :: Web Environment"
]
repository = "https://github.com/Chainlit/chainlit"
readme = "README.md"
exclude = ["chainlit/frontend/**/*", "chainlit/copilot/**/**/"]
include = ["chainlit/frontend/dist/**/*", "chainlit/copilot/dist/**/*"]

[tool.poetry.scripts]
# command_name = module_for_handler : function_for_handler
chainlit = 'chainlit.cli:cli'

[tool.poetry.build]
script = "build.py"

[tool.poetry.dependencies]
python = ">=3.10,<4.0.0"
httpx = ">=0.23.0"
literalai = "0.1.201"
dataclasses_json = "^0.6.7"
fastapi = ">=0.115.3,<0.116"
starlette = "^0.41.2"
uvicorn = ">=0.25.0"
python-socketio = "^5.11.0"
aiofiles = ">=23.1.0,<25.0.0"
syncer = "^2.0.3"
asyncer = "^0.0.7"
mcp = "^1.3.0"
nest-asyncio = "^1.6.0"
click = "^8.1.3"
tomli = "^2.0.1"
pydantic = ">=2.7.2,<3"
python-dotenv = "^1.0.0"
uptrace = "^1.29.0"
watchfiles = "^0.20.0"
filetype = "^1.2.0"
lazify = "^0.4.0"
packaging = ">=23.1"
python-multipart = "^0.0.18"
pyjwt = "^2.8.0"

[tool.poetry.group.tests]
optional = true

[tool.poetry.group.tests.dependencies]
pytest = "^8.3.2"
pytest-asyncio = "^0.23.8"
pytest-cov = "^5.0.0"
openai = "^1.11.1"
langchain = "^0.2.4"
llama-index = "^0.10.45"
semantic-kernel = "^1.24.0"
tenacity = "^8.4.1"
transformers = "^4.38"
matplotlib = "^3.7.1"
plotly = "^5.18.0"
slack_bolt = "^1.18.1"
discord = "^2.3.2"
botbuilder-core = "^4.15.0"
aiosqlite = "^0.20.0"
pandas = "^2.2.2"
moto = "^5.0.14"

[tool.poetry.group.dev.dependencies]
ruff = "^0.9.0"

[tool.poetry.group.mypy]
optional = true

[tool.poetry.group.mypy.dependencies]
mypy = "^1.13"
types-requests = "^********"
types-aiofiles = "^********"
mypy-boto3-dynamodb = "^1.34.113"
pandas-stubs = { version = "^2.2.2", python = ">=3.9" }

[tool.mypy]
python_version = "3.10"


[[tool.mypy.overrides]]
module = [
    "boto3.dynamodb.types",
    "botbuilder.*",
    "filetype",
    "langflow",
    "lazify",
    "plotly",
    "nest_asyncio",
    "socketio.*",
    "uptrace",
    "syncer",
    "azure.storage.filedatalake",
    "google-cloud-storage",
    "azure.storage.blob",
    "azure.storage.blob.aio",
    "google.oauth2"
]

ignore_missing_imports = true







[tool.poetry.group.custom-data]
optional = true

[tool.poetry.group.custom-data.dependencies]
asyncpg = "^0.29.0"
SQLAlchemy = "^2.0.28"
boto3 = "^1.34.73"
azure-identity = "^1.14.1"
azure-storage-file-datalake = "^12.14.0"
azure-storage-blob="^12.24.0"
google-cloud-storage = "^2.19.0"

[build-system]
requires = ["poetry-core==1.9.1"]
build-backend = "poetry.core.masonry.api"


[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.ruff]
target-version = "py39"

[tool.ruff.lint]
select = ["E", "F", "I", "LOG", "UP", "T10", "ISC", "ICN", "LOG", "G", "PIE", "PT", "Q", "RSE", "FURB", "RUF"]
ignore = ["E712", "E501", "UP006", "UP035","PIE790", "RUF005", "RUF006", "RUF012", "ISC001"]

[tool.ruff.lint.isort]
combine-as-imports = true
