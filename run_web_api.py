#!/usr/bin/env python3
"""
Web API启动脚本
启动FastAPI服务器提供Web API和WebSocket服务
"""

import asyncio
import sys
import os
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入必要模块
from core.config_manager import config_manager
from core.error_handler import error_handler
from core.agent_core import agent_core


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭Web API服务器...")
    sys.exit(0)


async def initialize_services():
    """初始化服务"""
    try:
        print("正在初始化服务...")
        
        # 初始化配置管理器
        print("- 加载配置...")
        config_manager.load_config('llm_config.json')
        config_manager.load_config('mcp_config.json') 
        config_manager.load_config('persistence_config.json')
        config_manager.load_config('web_config.json')
        
        # 验证配置
        print("- 验证配置...")
        validation_results = config_manager.validate_config()
        for config_type, is_valid in validation_results.items():
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"  {config_type}: {status}")
        
        # 检查是否有无效配置
        if not all(validation_results.values()):
            print("❌ 配置验证失败，请检查配置文件")
            return False
        
        # 初始化智能体核心
        print("- 初始化智能体核心...")
        await agent_core.initialize()
        
        print("✅ 服务初始化完成")
        return True
        
    except Exception as e:
        error_handler.handle_error(e, {"context": "service_initialization"})
        print(f"❌ 服务初始化失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 启动LangGraph Agent Web API服务器")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 异步初始化服务
    async def startup():
        success = await initialize_services()
        if not success:
            sys.exit(1)
        
        # 获取Web配置
        web_config = config_manager.get_web_config()
        host = web_config.get('host', '0.0.0.0')
        port = web_config.get('port', 8000)
        debug = web_config.get('debug', False)
        
        print(f"🌐 Web API服务器配置:")
        print(f"   主机: {host}")
        print(f"   端口: {port}")
        print(f"   调试模式: {debug}")
        print(f"   API文档: http://{host}:{port}/docs")
        print(f"   WebSocket: ws://{host}:{port}/ws/{{thread_id}}")
        print("=" * 50)
        
        # 启动FastAPI服务器
        try:
            import uvicorn
            from interfaces.web_api import app
            
            print("🎯 启动Web API服务器...")
            
            # 配置uvicorn
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                reload=debug,
                log_level="info" if not debug else "debug",
                access_log=True,
                use_colors=True
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except ImportError:
            print("❌ 缺少依赖: pip install uvicorn fastapi websockets")
            sys.exit(1)
        except Exception as e:
            error_handler.handle_error(e, {"context": "web_server_startup"})
            print(f"❌ Web API服务器启动失败: {e}")
            sys.exit(1)
    
    # 运行异步主函数
    try:
        asyncio.run(startup())
    except KeyboardInterrupt:
        print("\n👋 Web API服务器已停止")
    except Exception as e:
        error_handler.handle_error(e, {"context": "main_execution"})
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
