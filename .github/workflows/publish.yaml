name: Publish

on:
  workflow_dispatch:
  release:
    types: [published]

permissions: read-all

jobs:
  ci:
    uses: ./.github/workflows/ci.yaml
    secrets: inherit
  build-n-publish:
    name: Upload release to PyPI
    runs-on: ubuntu-latest
    needs: [ci]
    env:
      name: pypi
      url: https://pypi.org/p/chainlit
      BACKEND_DIR: ./backend
    permissions:
      contents: read
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing
    steps:
      - uses: actions/checkout@v4
        with:
          ref: main
      - uses: ./.github/actions/pnpm-node-install
        name: Install Node, pnpm and dependencies.
        with:
          pnpm-install-args: --no-frozen-lockfile
      - uses: ./.github/actions/poetry-python-install
        name: Install Python, poetry and Python dependencies
        with:
          poetry-working-directory: ${{ env.BACKEND_DIR }}
      - name: Build Python distribution
        run: poetry self add poetry-plugin-ignore-build-script && poetry build --ignore-build-script
        working-directory: ${{ env.BACKEND_DIR }}
      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: backend/dist
          password: ${{ secrets.PYPI_API_TOKEN }}
