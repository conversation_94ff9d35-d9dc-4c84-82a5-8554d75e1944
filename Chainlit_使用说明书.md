# Chainlit 使用说明书（小白版）

---

## 1. 项目简介

Chainlit 是一个开源框架，帮助你用 Python 快速开发对话式 AI 应用。它支持现代化聊天界面、丰富的扩展能力，并可集成主流大语言模型（如 OpenAI、LangChain 等）。

---

## 2. 环境准备

### 2.1 Python
建议安装 Python 3.10 及以上版本。

- macOS 用户可用 Homebrew 安装：
  ```bash
  brew install python@3.11
  ```
- Windows 用户可去 [Python 官网](https://www.python.org/downloads/) 下载并安装。

### 2.2 Poetry（Python 包和虚拟环境管理工具）

- 安装 Poetry：
  ```bash
  curl -sSL https://install.python-poetry.org | python3 -
  ```
- 配置环境变量（如未自动添加）：
  ```bash
  export PATH="$HOME/.local/bin:$PATH"
  # 建议加到 ~/.zshrc 或 ~/.bashrc 文件末尾
  source ~/.zshrc  # 或 source ~/.bashrc
  ```
- 验证安装：
  ```bash
  poetry --version
  ```

### 2.3 Node.js 和 pnpm（前端依赖管理）

- 安装 nvm（Node.js 版本管理工具）：
  ```bash
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
  source ~/.zshrc  # 或 source ~/.bashrc
  ```
- 安装 Node.js（推荐 18 及以上）：
  ```bash
  nvm install 18
  nvm use 18
  ```
- 安装 pnpm：
  ```bash
  npm install -g pnpm
  ```
- 检查版本：
  ```bash
  node -v
  npm -v
  pnpm -v
  ```

---

## 3. 仓库设置

### 3.1 Fork 仓库

1. 打开 [Chainlit 官方仓库](https://github.com/Chainlit/chainlit)
2. 点击右上角 **Fork**，将仓库 Fork 到你自己的 GitHub 账号下

### 3.2 克隆到本地

```bash
git clone https://github.com/你的用户名/chainlit.git
cd chainlit
```

### 3.3 添加上游仓库（方便同步官方更新）

```bash
git remote add upstream https://github.com/Chainlit/chainlit.git
git remote -v
```

### 3.4 安装依赖

#### 后端依赖

```bash
cd backend
poetry install --with tests --with mypy --with dev
```

#### 前端依赖（如需开发前端）

```bash
cd ../frontend
pnpm install
```

---

## 4. 快速启动

### 4.1 运行官方示例应用

#### 方式一：直接体验

```bash
pip install chainlit
chainlit hello
```
浏览器会自动打开示例应用。

#### 方式二：本地开发

1. 新建 `demo.py`，内容如下：
    ```python
    import chainlit as cl

    @cl.step(type="tool")
    async def tool():
        await cl.sleep(2)
        return "工具响应！"

    @cl.on_message
    async def main(message: cl.Message):
        tool_res = await tool()
        await cl.Message(content=tool_res).send()
    ```
2. 运行：
    ```bash
    chainlit run demo.py -w
    ```
3. 浏览器访问 `http://localhost:8000` 查看效果。

### 4.2 前后端本地开发

- 启动后端：
  ```bash
  cd backend
  poetry shell
  chainlit run chainlit/hello.py
  ```
- 启动前端（新开一个终端窗口）：
  ```bash
  cd frontend
  pnpm run dev --port 5174 --host
  ```
- 访问 `http://localhost:5174/` 查看前端界面。

---

## 5. 常见问题与解决办法

- **poetry: command not found**
  - 需先安装 Poetry，并配置好环境变量。
- **nvm: command not found**
  - 需先安装 nvm，并用 source ~/.zshrc 使其生效。
- **依赖安装慢/失败**
  - 可尝试多次，或更换国内镜像源。
- **端口被占用**
  - 换一个端口，如 `chainlit run demo.py --port 9000`。
- **其他问题**
  - 复制完整报错信息，搜索或发给开发者/社区求助。

---

## 6. 参考链接

- 官方文档：https://docs.chainlit.io
- GitHub 仓库：https://github.com/Chainlit/chainlit
- 中文社区/交流群：可在 GitHub issue 或 Discord 频道咨询

---

如有疑问，欢迎随时提问！祝你玩得开心 🚀 