{"compilerOptions": {"composite": true, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "baseUrl": "./src", "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["node"], "paths": {"client-types/*": ["../../libs/react-client/dist"], "@/*": ["./*"]}}, "include": ["./src"], "references": [{"path": "./tsconfig.node.json"}]}