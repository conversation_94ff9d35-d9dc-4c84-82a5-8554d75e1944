name: Install Python, poetry and dependencies.
description: Install Python, Poetry and poetry dependencies using cache

inputs:
  python-version:
    description: Python version
    required: true
    default: '3.10'
  poetry-version:
    description: Poetry version
    required: true
    default: '1.8.3'
  poetry-working-directory:
    description: Working directory for poetry command.
    required: false
    default: .
  poetry-install-args:
    description: Extra arguments for poetry install, e.g. --with tests.
    required: false

runs:
  using: composite
  steps:
    - name: Cache poetry install
      uses: actions/cache@v4
      with:
        path: ~/.local
        key: poetry-${{ runner.os }}-${{ inputs.poetry-version }}-0
    - name: Install Poetry
      run: pipx install 'poetry==${{ inputs.poetry-version }}'
      shell: bash
    - name: Set up Python ${{ inputs.python-version }}
      id: setup_python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}
        cache: poetry
        cache-dependency-path: ${{ inputs.poetry-working-directory }}/poetry.lock
    - name: Set Poetry environment
      run: poetry -C '${{ inputs.poetry-working-directory }}' env use '${{ steps.setup_python.outputs.python-path }}'
      shell: bash
    - name: Install Python dependencies
      run: poetry -C '${{ inputs.poetry-working-directory }}' install ${{ inputs.poetry-install-args }}
      shell: bash
