# Privacy Policy

## 📏 Telemetry

Chainlit collects specific metadata points by default to help us better understand and improve the package based on community usage. We greatly value your privacy and ensure that the metadata we collect [is limited](/backend/telemetry.py).

### 🕵️‍♀️ Scope

Chainlit collects the following metadata points:

- Count of SDK function calls
- Duration of SDK function calls

This information allows us to get an accurate representation of how the community uses Chainlit and make improvements accordingly.

### 🙅‍♀️ Opting Out of Telemetry

If you prefer not to share this metadata, you can easily opt out by setting `enable_telemetry = false` in your `.chainlit/config.toml` file. This will disable the telemetry feature and prevent any data from being collected.
