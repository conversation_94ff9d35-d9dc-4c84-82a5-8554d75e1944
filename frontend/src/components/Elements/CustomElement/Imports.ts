import * as LucideIcons from 'lucide-react';
import React from 'react';
import * as ReactHookForm from 'react-hook-form';
import * as Recoil from 'recoil';
import * as Sonner from 'sonner';
import * as Zod from 'zod';

import * as ChainlitReactClient from '@chainlit/react-client';

import * as Markdown from '@/components/Markdown';
import * as AccordionComponents from '@/components/ui/accordion';
import * as AspectRatioComponents from '@/components/ui/aspect-ratio';
import * as AvatarComponents from '@/components/ui/avatar';
import * as BadgeComponents from '@/components/ui/badge';
import * as ButtonComponents from '@/components/ui/button';
import * as CardComponents from '@/components/ui/card';
import * as CarouselComponents from '@/components/ui/carousel';
import * as CheckboxComponents from '@/components/ui/checkbox';
import * as CommandComponents from '@/components/ui/command';
import * as DialogComponents from '@/components/ui/dialog';
import * as DropdownMenuComponents from '@/components/ui/dropdown-menu';
import * as FormComponents from '@/components/ui/form';
import * as HoverCardComponents from '@/components/ui/hover-card';
import * as InputComponents from '@/components/ui/input';
import * as LabelComponents from '@/components/ui/label';
import * as PaginationComponents from '@/components/ui/pagination';
import * as PopoverComponents from '@/components/ui/popover';
import * as ProgressComponents from '@/components/ui/progress';
import * as ScrollAreaComponents from '@/components/ui/scroll-area';
import * as SelectComponents from '@/components/ui/select';
import * as SeparatorComponents from '@/components/ui/separator';
import * as SheetComponents from '@/components/ui/sheet';
import * as SkeletonComponents from '@/components/ui/skeleton';
import * as SwitchComponents from '@/components/ui/switch';
import * as TableComponents from '@/components/ui/table';
import * as TabsComponents from '@/components/ui/tabs';
import * as TextareaComponents from '@/components/ui/textarea';
import * as TooltipComponents from '@/components/ui/tooltip';

const Imports = {
  react: React,
  sonner: Sonner,
  zod: Zod,
  recoil: Recoil,
  '@chainlit/react-client': ChainlitReactClient,
  '@/components/markdown': Markdown,
  'react-hook-form': ReactHookForm,
  'lucide-react': LucideIcons,
  '@/components/ui/tabs': TabsComponents,
  '@/components/ui/accordion': AccordionComponents,
  '@/components/ui/aspect-ratio': AspectRatioComponents,
  '@/components/ui/avatar': AvatarComponents,
  '@/components/ui/badge': BadgeComponents,
  '@/components/ui/button': ButtonComponents,
  '@/components/ui/card': CardComponents,
  '@/components/ui/carousel': CarouselComponents,
  '@/components/ui/checkbox': CheckboxComponents,
  '@/components/ui/command': CommandComponents,
  '@/components/ui/dialog': DialogComponents,
  '@/components/ui/dropdown-menu': DropdownMenuComponents,
  '@/components/ui/form': FormComponents,
  '@/components/ui/hover-card': HoverCardComponents,
  '@/components/ui/input': InputComponents,
  '@/components/ui/label': LabelComponents,
  '@/components/ui/pagination': PaginationComponents,
  '@/components/ui/popover': PopoverComponents,
  '@/components/ui/progress': ProgressComponents,
  '@/components/ui/scroll-area': ScrollAreaComponents,
  '@/components/ui/separator': SeparatorComponents,
  '@/components/ui/select': SelectComponents,
  '@/components/ui/sheet': SheetComponents,
  '@/components/ui/skeleton': SkeletonComponents,
  '@/components/ui/switch': SwitchComponents,
  '@/components/ui/table': TableComponents,
  '@/components/ui/textarea': TextareaComponents,
  '@/components/ui/tooltip': TooltipComponents
};

export default Imports;
