name: CI

on:
  workflow_call:
  workflow_dispatch:
  merge_group:
  pull_request:
    branches: [main, dev, 'release/**']
  push:
    branches: [main, dev, 'release/**']

permissions: read-all

jobs:
  pytest:
    uses: ./.github/workflows/pytest.yaml
    secrets: inherit
  lint-backend:
    uses: ./.github/workflows/lint-backend.yaml
    secrets: inherit
  e2e-tests:
    uses: ./.github/workflows/e2e-tests.yaml
    secrets: inherit
  lint-ui:
    uses: ./.github/workflows/lint-ui.yaml
    secrets: inherit
  ci:
    runs-on: ubuntu-latest
    name: Run CI
    if: always()  # This ensures the job always runs
    needs: [lint-backend, pytest, lint-ui, e2e-tests]
    steps:
      # Propagate failure
      - name: Check dependent jobs
        if: contains(needs.*.result, 'success') != true || contains(needs.*.result, 'skipped')
        run: |
          echo "Not all required jobs succeeded"
          exit 1
