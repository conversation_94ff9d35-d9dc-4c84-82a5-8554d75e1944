export const Okta = () => {
  return (
    <svg
      version="1.2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1594 1595"
      width="24"
      height="24"
      style={{ fill: 'inherit' }}
    >
      <path
        fillRule="evenodd"
        d="m877 11.5l-32.8 403.8c-15.5-1.8-31-2.7-46.9-2.7-19.9 0-39.4 1.3-58.5 4.4l-18.6-195.6c-0.4-6.2 4.5-11.6 10.7-11.6h33.2l-16-197.8c-0.4-6.2 4.5-11.6 10.2-11.6h108.5c6.2 0 11.1 5.4 10.2 11.1zm-166.1 410.4c-35 8-68.2 20.8-98.8 37.6l-84.6-177.5c-2.6-5.3 0-11.9 5.8-14.2l31.4-11.5-82.8-180.6c-2.6-5.3 0-11.9 5.8-14.2l101.9-37.2c5.7-2.2 11.9 1.4 13.7 7.1 0.4 0 107.6 390.5 107.6 390.5zm-357.4-278l234.3 330.2c-29.7 19.5-56.7 42.5-79.7 69.1l-140.5-138.1c-4.4-4.4-3.9-11.5 0.5-15.5l25.7-21.3-139.6-141.2c-4.4-4.4-3.9-11.5 0.9-15.5l82.9-69.5c4.8-4 11.5-3.1 15 1.8zm136.9 421.4c-21.3 27.9-38.5 58.9-51.4 92.1l-178.9-81.9c-5.8-2.2-8-9.3-4.9-14.6l16.8-28.8-179.8-85c-5.3-2.6-7.5-9.3-4.4-14.6l54-93.8c3.1-5.3 10.2-7.1 15.1-3.6zm-466 24.8c0.9-6.2 7.1-9.7 12.8-8.4l392 102.3c-10.2 33.2-15.9 68.2-16.8 104.5l-196.2-16c-6.2-0.4-10.7-6.2-9.3-12.4l5.7-32.7-198-18.6c-6.2-0.5-10.1-6.2-9.3-12.4l18.6-106.7zm-15 264.7l403.5-37.2c1.8 35.9 8.9 70.9 19.9 103.6l-189.5 52.3c-5.8 1.3-12-2.2-12.9-8.4l-5.7-32.8-192.3 50c-5.7 1.4-11.9-2.2-12.8-8.4l-19.1-106.7c-0.9-6.2 3.1-11.9 9.3-12.4zm63.4 280.7c-3.1-5.3-0.9-11.9 4.4-14.6l365.9-173.5c13.7 32.7 32.3 63.3 54.4 90.7l-160.3 114.2c-4.9 3.6-12 2.2-15.1-3.1l-16.8-29.2-163.4 112.9c-4.9 3.5-12 1.8-15.1-3.5 0 0-54.5-93.9-54-93.9zm525.7-9.3l-111.6 162c-3.5 5.4-10.6 6.2-15.5 2.3l-25.7-21.7-115.1 162c-3.6 4.9-10.2 5.8-15.1 1.8l-83.3-69.5c-4.8-4-5.3-11.1-0.9-15.5l284.8-288.2c24.4 25.6 52.3 48.2 82.4 66.8zm-138.6 395.8c-5.8-2.2-8.4-8.9-5.8-14.2l168.8-368.3c31 15.9 64.7 27.9 99.7 34.5l-49.7 190.4c-1.3 5.7-7.9 9.3-13.7 7.1l-31.4-11.5-52.7 191.7c-1.8 5.7-8 9.3-13.8 7l-101.8-37.1zm337.5-340c19.9 0 39.4-1.4 58.4-4.5l18.6 195.7c0.5 6.2-4.4 11.5-10.6 11.5h-33.2l15.9 197.9c0.9 6.2-3.9 11.5-10.1 11.5h-108.6c-5.7 0-10.6-5.3-10.2-11.5l32.8-403.7c15.5 2.2 31 3.1 47 3.1zm174.5-728.3c-31-15.5-64.2-27.5-99.7-34.5l49.6-190.4c1.8-5.8 8-9.3 13.8-7.1l31.4 11.5 52.7-191.7c1.8-5.7 8-9.3 13.8-7.1l101.8 37.2c5.8 2.2 8.5 8.4 5.8 14.2zm391.6-207.2l-284.9 288.2c-23.9-25.7-51.3-48.2-81.9-66.8l111.6-162.1c3.6-4.8 10.7-6.2 15.5-2.2l25.7 21.7 115.2-162c3.5-4.9 10.6-5.8 15-1.8l83.3 69.5c4.9 4 4.9 11.1 0.5 15.5zm153.7 227.1l-365.9 173.6c-14.2-32.8-32.3-63.4-54.5-90.8l160.3-114.2c4.9-4 12-2.2 15.1 3.1l16.8 28.8 163.5-112.9c4.9-3.1 11.9-1.8 15 3.5l54.5 93.9c3.1 5.3 1.4 11.9-4.4 14.6zm58 146.5l18.6 106.7c0.9 6.2-3.1 11.5-9.3 12.4l-403.5 37.6c-1.8-36.3-8.9-70.8-19.9-103.6l189.6-52.2c5.7-1.8 11.9 2.2 12.8 8.4l5.8 32.8 192.2-50.1c5.8-1.3 12 2.3 12.8 8.5zm-18.6 391.3l-392-102.3c10.2-33.2 16-68.1 16.9-104.4l196.2 15.9c6.2 0.9 10.2 6.2 9.3 12.4l-5.8 32.8 198 18.6c6.2 0.8 10.2 6.1 9.3 12.3l-18.6 106.7c-0.9 6.2-7.1 9.8-12.8 8.5zm-104.1 243.9c-3.1 5.3-10.2 6.6-15.1 3.5l-333.5-230.2c21.3-27.9 38.5-58.9 51.4-92.1l178.9 81.9c5.8 2.7 8 9.3 4.9 14.6l-16.8 28.8 179.8 85c5.3 2.7 7.5 9.3 4.4 14.6zm-446.5-135.9c29.7-19 56.3-42.5 79.8-69l140.4 138.1c4.4 4.4 4.4 11.5-0.5 15.5l-25.7 21.2 139.6 141.3c4 4.4 4 11.5-0.9 15.4l-82.8 69.6c-4.5 3.9-11.6 3.1-15.1-1.8l-234.3-330.3zm-1.8 449.8c-5.7 2.2-11.9-1.3-13.7-7.1l-107.2-390.4c35-8 68.2-20.8 98.8-37.7l84.6 177.6c2.6 5.7 0 12.4-5.8 14.1l-31.4 11.5 82.8 180.7c2.6 5.7 0 11.9-5.8 14.1l-101.8 37.2z"
      />
    </svg>
  );
};
