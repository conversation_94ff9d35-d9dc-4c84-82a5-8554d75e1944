"""
统一错误处理器
提供统一的错误处理、日志记录和异常管理机制
"""

import logging
import traceback
import sys
from typing import Optional, Dict, Any, Callable
from functools import wraps
from enum import Enum
import json
from datetime import datetime


class ErrorLevel(Enum):
    """错误级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误
    UNKNOWN_ERROR = "E0001"
    VALIDATION_ERROR = "E0002"
    CONFIGURATION_ERROR = "E0003"
    
    # LLM相关错误
    LLM_CONNECTION_ERROR = "E1001"
    LLM_API_ERROR = "E1002"
    LLM_TIMEOUT_ERROR = "E1003"
    LLM_QUOTA_ERROR = "E1004"
    
    # MCP相关错误
    MCP_CONNECTION_ERROR = "E2001"
    MCP_TOOL_ERROR = "E2002"
    MCP_TIMEOUT_ERROR = "E2003"
    
    # 持久化相关错误
    DATABASE_CONNECTION_ERROR = "E3001"
    DATABASE_OPERATION_ERROR = "E3002"
    SESSION_ERROR = "E3003"
    
    # Web API相关错误
    API_VALIDATION_ERROR = "E4001"
    API_AUTHENTICATION_ERROR = "E4002"
    API_AUTHORIZATION_ERROR = "E4003"
    API_RATE_LIMIT_ERROR = "E4004"
    WEBSOCKET_ERROR = "E4005"


class CustomError(Exception):
    """自定义异常基类"""
    
    def __init__(self, message: str, error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR, 
                 details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.now()
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }
    
    def to_json(self) -> str:
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, logger_name: str = "ErrorHandler"):
        self.logger = logging.getLogger(logger_name)
        self._setup_logger()
        self.error_callbacks: Dict[ErrorCode, Callable] = {}
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def register_error_callback(self, error_code: ErrorCode, callback: Callable):
        """
        注册错误回调函数
        
        Args:
            error_code: 错误代码
            callback: 回调函数
        """
        self.error_callbacks[error_code] = callback
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> CustomError:
        """
        处理错误
        
        Args:
            error: 原始异常
            context: 错误上下文信息
            
        Returns:
            CustomError: 处理后的自定义错误
        """
        context = context or {}
        
        # 如果已经是CustomError，直接返回
        if isinstance(error, CustomError):
            custom_error = error
        else:
            # 根据异常类型映射错误代码
            error_code = self._map_error_code(error)
            custom_error = CustomError(
                message=str(error),
                error_code=error_code,
                details={
                    'original_exception': type(error).__name__,
                    'traceback': traceback.format_exc(),
                    'context': context
                }
            )
        
        # 记录错误日志
        self._log_error(custom_error)
        
        # 执行错误回调
        if custom_error.error_code in self.error_callbacks:
            try:
                self.error_callbacks[custom_error.error_code](custom_error)
            except Exception as callback_error:
                self.logger.error(f"错误回调执行失败: {callback_error}")
        
        return custom_error
    
    def _map_error_code(self, error: Exception) -> ErrorCode:
        """
        根据异常类型映射错误代码
        
        Args:
            error: 异常对象
            
        Returns:
            ErrorCode: 对应的错误代码
        """
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        # 根据异常类型和消息内容映射错误代码
        if 'connection' in error_message or 'network' in error_message:
            if 'database' in error_message or 'sqlite' in error_message:
                return ErrorCode.DATABASE_CONNECTION_ERROR
            elif 'mcp' in error_message:
                return ErrorCode.MCP_CONNECTION_ERROR
            else:
                return ErrorCode.LLM_CONNECTION_ERROR
        
        elif 'timeout' in error_message:
            if 'mcp' in error_message:
                return ErrorCode.MCP_TIMEOUT_ERROR
            else:
                return ErrorCode.LLM_TIMEOUT_ERROR
        
        elif 'api' in error_message or 'http' in error_message:
            if 'quota' in error_message or 'limit' in error_message:
                return ErrorCode.LLM_QUOTA_ERROR
            elif 'auth' in error_message:
                return ErrorCode.API_AUTHENTICATION_ERROR
            else:
                return ErrorCode.LLM_API_ERROR
        
        elif 'validation' in error_message or 'invalid' in error_message:
            return ErrorCode.VALIDATION_ERROR
        
        elif 'config' in error_message:
            return ErrorCode.CONFIGURATION_ERROR
        
        elif 'database' in error_message or 'sqlite' in error_message:
            return ErrorCode.DATABASE_OPERATION_ERROR
        
        elif 'session' in error_message:
            return ErrorCode.SESSION_ERROR
        
        elif 'websocket' in error_message:
            return ErrorCode.WEBSOCKET_ERROR
        
        else:
            return ErrorCode.UNKNOWN_ERROR
    
    def _log_error(self, error: CustomError):
        """
        记录错误日志
        
        Args:
            error: 自定义错误对象
        """
        log_level = self._get_log_level(error.error_code)
        log_message = f"[{error.error_code.value}] {error.message}"
        
        if log_level == ErrorLevel.DEBUG:
            self.logger.debug(log_message)
        elif log_level == ErrorLevel.INFO:
            self.logger.info(log_message)
        elif log_level == ErrorLevel.WARNING:
            self.logger.warning(log_message)
        elif log_level == ErrorLevel.ERROR:
            self.logger.error(log_message)
        elif log_level == ErrorLevel.CRITICAL:
            self.logger.critical(log_message)
        
        # 如果有详细信息，也记录下来
        if error.details:
            self.logger.debug(f"错误详情: {json.dumps(error.details, ensure_ascii=False, indent=2)}")
    
    def _get_log_level(self, error_code: ErrorCode) -> ErrorLevel:
        """
        根据错误代码获取日志级别
        
        Args:
            error_code: 错误代码
            
        Returns:
            ErrorLevel: 日志级别
        """
        critical_errors = [
            ErrorCode.DATABASE_CONNECTION_ERROR,
            ErrorCode.CONFIGURATION_ERROR
        ]
        
        warning_errors = [
            ErrorCode.LLM_TIMEOUT_ERROR,
            ErrorCode.MCP_TIMEOUT_ERROR,
            ErrorCode.LLM_QUOTA_ERROR
        ]
        
        if error_code in critical_errors:
            return ErrorLevel.CRITICAL
        elif error_code in warning_errors:
            return ErrorLevel.WARNING
        else:
            return ErrorLevel.ERROR


def error_handler_decorator(error_handler: ErrorHandler, 
                          context_func: Optional[Callable] = None):
    """
    错误处理装饰器
    
    Args:
        error_handler: 错误处理器实例
        context_func: 获取上下文信息的函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = context_func(*args, **kwargs) if context_func else {}
                custom_error = error_handler.handle_error(e, context)
                raise custom_error
        return wrapper
    return decorator


def async_error_handler_decorator(error_handler: ErrorHandler,
                                context_func: Optional[Callable] = None):
    """
    异步错误处理装饰器
    
    Args:
        error_handler: 错误处理器实例
        context_func: 获取上下文信息的函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = context_func(*args, **kwargs) if context_func else {}
                custom_error = error_handler.handle_error(e, context)
                raise custom_error
        return wrapper
    return decorator


# 全局错误处理器实例
error_handler = ErrorHandler()
