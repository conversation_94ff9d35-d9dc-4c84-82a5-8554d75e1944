{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "baseUrl": "./src", "paths": {"@/*": ["./*"]}, "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "sourceMap": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["node"]}, "include": ["./src", "./stories"], "references": [{"path": "./tsconfig.node.json"}]}