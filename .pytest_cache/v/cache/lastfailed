{"tests/unit/test_core_components.py::TestConfigManager::test_load_llm_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_load_mcp_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_load_persistence_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_invalid_config_path": true, "tests/unit/test_core_components.py::TestSessionManager::test_create_session": true, "tests/unit/test_core_components.py::TestSessionManager::test_get_session_config": true, "tests/unit/test_core_components.py::TestSessionManager::test_list_sessions": true, "tests/unit/test_core_components.py::TestSessionManager::test_session_cleanup": true, "tests/unit/test_core_components.py::TestAgentCore::test_agent_initialization": true, "tests/unit/test_core_components.py::TestAgentCore::test_create_workflow": true, "tests/unit/test_core_components.py::TestAgentCore::test_process_message": true, "tests/unit/test_core_components.py::TestUtilities::test_generate_session_id": true, "tests/unit/test_core_components.py::TestUtilities::test_validate_config": true, "tests/unit/test_core_components.py::TestPerformance::test_config_loading_performance": true, "tests/unit/test_core_components.py::TestPerformance::test_session_creation_performance": true, "tests/integration/test_cli_integration.py::TestCLIIntegration::test_cli_startup": true, "tests/integration/test_cli_integration.py::TestCLIIntegration::test_session_management_integration": true, "tests/integration/test_cli_integration.py::TestCLIIntegration::test_agent_workflow_integration": true, "tests/integration/test_cli_integration.py::TestCLIIntegration::test_persistence_integration": true, "tests/integration/test_cli_integration.py::TestCLIIntegration::test_tool_loading_integration": true, "tests/integration/test_cli_integration.py::TestWebAPIIntegration::test_fastapi_startup": true, "tests/integration/test_cli_integration.py::TestWebAPIIntegration::test_websocket_integration": true, "tests/integration/test_cli_integration.py::TestLangGraphIntegration::test_complete_workflow_integration": true, "tests/integration/test_cli_integration.py::TestLangGraphIntegration::test_persistence_workflow_integration": true, "tests/integration/test_cli_integration.py::TestSystemIntegration::test_full_system_startup": true, "tests/integration/test_cli_integration.py::TestSystemIntegration::test_concurrent_sessions": true}