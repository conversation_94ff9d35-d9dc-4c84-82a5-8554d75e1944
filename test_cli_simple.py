#!/usr/bin/env python3
"""
简单的CLI功能测试
"""

import asyncio
import sys
import os

async def test_cli():
    try:
        print('🧪 测试CLI初始化...')
        
        # 导入主模块
        import main
        
        # 测试Agent初始化
        app, tools, session_manager = await main.initialize_agent()
        print(f'✅ Agent初始化成功')
        print(f'✅ 工具数量: {len(tools)}')
        print(f'✅ 会话管理器类型: {type(session_manager).__name__}')
        
        # 测试会话创建
        thread_id = session_manager.create_session('test_user')
        print(f'✅ 会话创建成功: {thread_id}')
        
        # 测试配置获取
        config = session_manager.get_session_config(thread_id)
        print(f'✅ 会话配置获取成功: {list(config.keys())}')
        
        # 测试LangGraph标准接口
        if hasattr(app, 'ainvoke') and hasattr(app, 'aget_state'):
            print('✅ LangGraph标准接口验证通过')
        else:
            print('❌ LangGraph标准接口验证失败')
            return False
        
        print('🎉 CLI核心功能测试通过！')
        return True
        
    except Exception as e:
        print(f'❌ CLI测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_cli())
    sys.exit(0 if result else 1)
