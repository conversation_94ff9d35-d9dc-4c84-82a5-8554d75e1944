["tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_complete_cli_conversation_workflow", "tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_multi_session_workflow", "tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_session_persistence_across_restarts", "tests/e2e/test_complete_workflows.py::TestPerformanceWorkflows::test_high_load_workflow", "tests/e2e/test_complete_workflows.py::TestPerformanceWorkflows::test_memory_usage_workflow", "tests/e2e/test_complete_workflows.py::TestRobustnessWorkflows::test_error_recovery_workflow", "tests/e2e/test_complete_workflows.py::TestWebAPIWorkflows::test_complete_api_workflow", "tests/e2e/test_complete_workflows.py::TestWebAPIWorkflows::test_websocket_workflow", "tests/integration/test_cli_integration.py::TestCLIIntegration::test_agent_workflow_integration", "tests/integration/test_cli_integration.py::TestCLIIntegration::test_cli_startup", "tests/integration/test_cli_integration.py::TestCLIIntegration::test_persistence_integration", "tests/integration/test_cli_integration.py::TestCLIIntegration::test_session_management_integration", "tests/integration/test_cli_integration.py::TestCLIIntegration::test_tool_loading_integration", "tests/integration/test_cli_integration.py::TestLangGraphIntegration::test_complete_workflow_integration", "tests/integration/test_cli_integration.py::TestLangGraphIntegration::test_persistence_workflow_integration", "tests/integration/test_cli_integration.py::TestSystemIntegration::test_concurrent_sessions", "tests/integration/test_cli_integration.py::TestSystemIntegration::test_full_system_startup", "tests/integration/test_cli_integration.py::TestWebAPIIntegration::test_fastapi_startup", "tests/integration/test_cli_integration.py::TestWebAPIIntegration::test_websocket_integration", "tests/unit/test_core_components.py::TestAgentCore::test_agent_initialization", "tests/unit/test_core_components.py::TestAgentCore::test_create_workflow", "tests/unit/test_core_components.py::TestAgentCore::test_process_message", "tests/unit/test_core_components.py::TestConfigManager::test_invalid_config_path", "tests/unit/test_core_components.py::TestConfigManager::test_load_llm_config", "tests/unit/test_core_components.py::TestConfigManager::test_load_mcp_config", "tests/unit/test_core_components.py::TestConfigManager::test_load_persistence_config", "tests/unit/test_core_components.py::TestPerformance::test_config_loading_performance", "tests/unit/test_core_components.py::TestPerformance::test_session_creation_performance", "tests/unit/test_core_components.py::TestSessionManager::test_create_session", "tests/unit/test_core_components.py::TestSessionManager::test_get_session_config", "tests/unit/test_core_components.py::TestSessionManager::test_list_sessions", "tests/unit/test_core_components.py::TestSessionManager::test_session_cleanup", "tests/unit/test_core_components.py::TestUtilities::test_generate_session_id", "tests/unit/test_core_components.py::TestUtilities::test_validate_config", "tests/unit/test_simple_components.py::TestAgentCore::test_agent_core_initialization", "tests/unit/test_simple_components.py::TestConfigManager::test_config_manager_initialization", "tests/unit/test_simple_components.py::TestConfigManager::test_config_validation", "tests/unit/test_simple_components.py::TestConfigManager::test_get_llm_config", "tests/unit/test_simple_components.py::TestConfigManager::test_get_mcp_config", "tests/unit/test_simple_components.py::TestConfigManager::test_get_persistence_config", "tests/unit/test_simple_components.py::TestErrorHandler::test_error_handler_import", "tests/unit/test_simple_components.py::TestImports::test_langchain_imports", "tests/unit/test_simple_components.py::TestImports::test_langgraph_imports", "tests/unit/test_simple_components.py::TestMainModule::test_main_module_import", "tests/unit/test_simple_components.py::TestMainModule::test_persistence_config_loading", "tests/unit/test_simple_components.py::TestMainModule::test_session_manager_class", "tests/unit/test_simple_components.py::TestUtilities::test_path_operations", "tests/unit/test_simple_components.py::TestUtilities::test_uuid_generation"]