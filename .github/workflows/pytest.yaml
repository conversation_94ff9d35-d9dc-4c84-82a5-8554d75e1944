name: Pytest

on: [workflow_call]

permissions: read-all

jobs:
  pytest:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
        fastapi-version: ['0.115']
    env:
      BACKEND_DIR: ./backend
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/pnpm-node-install
        name: Install Node, pnpm and dependencies.
      - uses: ./.github/actions/poetry-python-install
        name: Install Python, poetry and Python dependencies
        with:
          python-version: ${{ matrix.python-version }}
          poetry-install-args: --with tests --with mypy --with custom-data
          poetry-working-directory: ${{ env.BACKEND_DIR }}
      - name: Install fastapi ${{ matrix.fastapi-version }}
        run: poetry add fastapi@^${{ matrix.fastapi-version}}
        working-directory: ${{ env.BACKEND_DIR }}
      - name: Run Pytest
        run: poetry run pytest --cov=chainlit/
        working-directory: ${{ env.BACKEND_DIR }}
