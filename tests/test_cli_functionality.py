#!/usr/bin/env python3
"""
CLI功能完整性测试 - 确保原有功能没有破坏性变更
严格遵循LangGraph官方标准
"""

import asyncio
import os
import sys
import tempfile
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入主模块
try:
    import main
    from llm_loader import load_llm_from_config
    from mcp_loader import load_mcp_tools_from_config
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在项目根目录运行测试")
    sys.exit(1)


class CLIFunctionalityTester:
    """CLI功能测试器"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        self.original_cwd = os.getcwd()
        
    async def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置测试环境...")
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="cli_test_")
        print(f"📁 临时测试目录: {self.temp_dir}")
        
        # 复制必要的配置文件到测试目录
        config_files = [
            "llm_config.json",
            "mcp_config.json", 
            "persistence_config.json"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, self.temp_dir)
                print(f"✅ 复制配置文件: {config_file}")
            else:
                print(f"⚠️ 配置文件不存在: {config_file}")
        
        # 切换到测试目录
        os.chdir(self.temp_dir)
        
    def cleanup_test_environment(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        os.chdir(self.original_cwd)
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print(f"🗑️ 删除临时目录: {self.temp_dir}")
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "status": status
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {details}")
    
    async def test_config_loading(self):
        """测试配置加载功能"""
        print("\n📋 测试配置加载功能...")

        try:
            # 导入main模块的函数
            from main import load_persistence_config

            # 测试持久化配置加载
            persistence_config = load_persistence_config()
            self.log_test_result(
                "持久化配置加载",
                isinstance(persistence_config, dict),
                f"配置类型: {type(persistence_config)}"
            )

            # 测试LLM配置加载
            try:
                llm = load_llm_from_config("llm_config.json")
                self.log_test_result(
                    "LLM配置加载",
                    llm is not None,
                    f"LLM类型: {type(llm).__name__}"
                )
            except Exception as e:
                self.log_test_result(
                    "LLM配置加载",
                    False,
                    f"错误: {str(e)}"
                )

            # 测试MCP工具配置加载
            try:
                _, tools = await load_mcp_tools_from_config("mcp_config.json")
                self.log_test_result(
                    "MCP工具配置加载",
                    isinstance(tools, list),
                    f"工具数量: {len(tools) if tools else 0}"
                )
            except Exception as e:
                self.log_test_result(
                    "MCP工具配置加载",
                    False,
                    f"错误: {str(e)}"
                )

        except Exception as e:
            self.log_test_result(
                "配置加载总体测试",
                False,
                f"严重错误: {str(e)}"
            )
    
    async def test_checkpointer_creation(self):
        """测试检查点存储器创建 - LangGraph官方标准"""
        print("\n💾 测试检查点存储器创建...")
        
        try:
            # 导入main模块的函数
            from main import load_persistence_config, create_checkpointer

            # 测试默认配置
            persistence_config = load_persistence_config()
            checkpointer = await create_checkpointer(persistence_config)
            
            self.log_test_result(
                "检查点存储器创建",
                checkpointer is not None,
                f"存储器类型: {type(checkpointer).__name__}"
            )
            
            # 验证是否遵循LangGraph官方标准
            if hasattr(checkpointer, 'aget') and hasattr(checkpointer, 'aput'):
                self.log_test_result(
                    "LangGraph标准接口验证",
                    True,
                    "包含aget和aput方法"
                )
            else:
                self.log_test_result(
                    "LangGraph标准接口验证",
                    False,
                    "缺少标准接口方法"
                )
                
        except Exception as e:
            self.log_test_result(
                "检查点存储器创建",
                False,
                f"错误: {str(e)}"
            )
    
    async def test_session_manager(self):
        """测试会话管理器功能"""
        print("\n👥 测试会话管理器功能...")
        
        try:
            # 导入main模块的类
            from main import SimpleSessionManager

            # 创建会话管理器
            session_manager = SimpleSessionManager()
            
            # 测试创建会话
            thread_id = session_manager.create_session("test_user")
            self.log_test_result(
                "会话创建",
                thread_id is not None and "test_user" in thread_id,
                f"会话ID: {thread_id}"
            )
            
            # 测试获取会话配置
            config = session_manager.get_session_config(thread_id)
            expected_keys = ["configurable"]
            has_required_keys = all(key in config for key in expected_keys)
            self.log_test_result(
                "会话配置获取",
                has_required_keys,
                f"配置结构: {list(config.keys())}"
            )
            
            # 测试会话恢复
            resumed_id = session_manager.resume_session(thread_id)
            self.log_test_result(
                "会话恢复",
                resumed_id == thread_id,
                f"恢复的会话ID: {resumed_id}"
            )
            
            # 测试清除会话
            new_thread_id = session_manager.clear_current_session()
            self.log_test_result(
                "会话清除",
                new_thread_id != thread_id,
                f"新会话ID: {new_thread_id}"
            )
            
        except Exception as e:
            self.log_test_result(
                "会话管理器测试",
                False,
                f"错误: {str(e)}"
            )
    
    async def test_agent_initialization(self):
        """测试Agent初始化"""
        print("\n🤖 测试Agent初始化...")
        
        try:
            # 导入main模块的函数
            from main import initialize_agent

            # 测试完整的Agent初始化
            app, tools, session_manager = await initialize_agent()
            
            self.log_test_result(
                "Agent应用初始化",
                app is not None,
                f"应用类型: {type(app).__name__}"
            )
            
            self.log_test_result(
                "工具加载",
                isinstance(tools, list),
                f"工具数量: {len(tools)}"
            )
            
            self.log_test_result(
                "会话管理器初始化",
                session_manager is not None,
                f"管理器类型: {type(session_manager).__name__}"
            )
            
            # 验证LangGraph标准结构
            if hasattr(app, 'ainvoke') and hasattr(app, 'aget_state'):
                self.log_test_result(
                    "LangGraph应用标准接口",
                    True,
                    "包含ainvoke和aget_state方法"
                )
            else:
                self.log_test_result(
                    "LangGraph应用标准接口",
                    False,
                    "缺少标准接口方法"
                )
                
        except Exception as e:
            self.log_test_result(
                "Agent初始化",
                False,
                f"错误: {str(e)}"
            )
    
    async def test_basic_conversation(self):
        """测试基本对话功能"""
        print("\n💬 测试基本对话功能...")
        
        try:
            # 导入main模块的函数
            from main import initialize_agent

            # 初始化Agent
            app, tools, session_manager = await initialize_agent()
            
            # 创建测试会话
            thread_id = session_manager.create_session("test_user")
            config = session_manager.get_session_config(thread_id)
            
            # 测试简单对话
            test_message = "你好，这是一个测试消息"
            
            # 模拟run_agent_with_persistence函数的核心逻辑
            from langchain_core.messages import HumanMessage
            
            # 获取当前状态
            current_state = await app.aget_state(config)
            
            # 添加用户消息
            await app.aupdate_state(config, {"messages": [HumanMessage(content=test_message)]})
            
            # 运行Agent
            result = await app.ainvoke(None, config)
            
            self.log_test_result(
                "基本对话功能",
                result is not None,
                f"对话结果类型: {type(result)}"
            )
            
            # 验证状态持久化
            final_state = await app.aget_state(config)
            has_messages = final_state.values and "messages" in final_state.values
            self.log_test_result(
                "状态持久化",
                has_messages,
                f"消息数量: {len(final_state.values.get('messages', [])) if has_messages else 0}"
            )
            
        except Exception as e:
            self.log_test_result(
                "基本对话功能",
                False,
                f"错误: {str(e)}"
            )
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("🧪 CLI功能完整性测试总结")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📈 通过率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  • {result['test']}: {result['details']}")
        
        print("\n" + "="*60)
        
        # 保存测试报告
        report_path = os.path.join(self.original_cwd, "tests", "cli_test_report.json")
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "pass_rate": passed_tests/total_tests*100
                },
                "results": self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"📄 测试报告已保存: {report_path}")
        
        return failed_tests == 0


async def main():
    """主测试函数"""
    print("🚀 开始CLI功能完整性测试")
    print("严格验证LangGraph官方标准合规性")
    print("="*60)
    
    tester = CLIFunctionalityTester()
    
    try:
        # 设置测试环境
        await tester.setup_test_environment()
        
        # 运行所有测试
        await tester.test_config_loading()
        await tester.test_checkpointer_creation()
        await tester.test_session_manager()
        await tester.test_agent_initialization()
        await tester.test_basic_conversation()
        
        # 打印测试总结
        success = tester.print_test_summary()
        
        if success:
            print("\n🎉 所有CLI功能测试通过！原有功能完全正常！")
            return 0
        else:
            print("\n⚠️ 部分测试失败，需要修复问题")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
        
    finally:
        # 清理测试环境
        tester.cleanup_test_environment()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
