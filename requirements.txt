# Core LangGraph and LangChain dependencies
langgraph>=0.2.0
langchain>=0.3.0
langchain-core>=0.3.0
langchain-community>=0.3.0

# LLM providers
langchain-openai>=0.2.0
langchain-anthropic>=0.2.0

# Persistence backends
langgraph-checkpoint-sqlite>=2.0.0
aiosqlite>=0.20.0

# MCP (Model Context Protocol)
mcp>=1.0.0

# Web API dependencies
fastapi>=0.115.0
uvicorn[standard]>=0.32.0
websockets>=13.0
pydantic>=2.9.0

# HTTP client
httpx>=0.27.0
aiohttp>=3.10.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0.0
click>=8.1.0
rich>=13.0.0

# Development and testing
pytest>=8.0.0
pytest-asyncio>=0.24.0
pytest-mock>=3.14.0
