{"name": "@chainlit/copilot", "private": true, "version": "2.4.1", "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint ./src --ext .ts,.tsx", "format": "prettier 'src/**/*.{ts,tsx}' --write", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@chainlit/app": "workspace:^", "@chainlit/react-client": "workspace:^", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.9.0", "i18next": "^23.7.16", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.0.0", "recoil": "^0.7.7", "sonner": "^1.2.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0"}, "devDependencies": {"@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@swc/core": "^1.3.86", "@types/lodash": "^4.14.199", "@types/node": "^20.5.7", "@types/react": "^18.3.1", "@types/react-file-icon": "^1.0.2", "@types/uuid": "^9.0.3", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "storybook": "^8.4.7", "tailwindcss": "^3.4.16", "typescript": "^5.2.2", "vite": "^5.4.14", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.0"}, "pnpm": {"overrides": {"vite@>=4.4.0 <4.4.12": ">=4.4.12", "vite@>=4.0.0 <=4.5.1": ">=4.5.2", "express@<4.19.2": ">=4.19.2", "vite@>=4.0.0 <=4.5.2": ">=4.5.3", "tar@<6.2.1": ">=6.2.1", "braces@<3.0.3": ">=3.0.3", "ejs@<3.1.10": ">=3.1.10", "ws@>=8.0.0 <8.17.1": ">=8.17.1", "micromatch@<4.0.8": ">=4.0.8", "body-parser@<1.20.3": ">=1.20.3", "send@<0.19.0": ">=0.19.0", "serve-static@<1.16.0": ">=1.16.0", "express@<4.20.0": ">=4.20.0", "path-to-regexp@<0.1.10": ">=0.1.10", "vite@>=4.0.0 <4.5.4": ">=4.5.4", "vite@>=4.0.0 <=4.5.3": ">=4.5.4", "rollup@>=3.0.0 <3.29.5": ">=3.29.5", "cookie@<0.7.0": ">=0.7.0", "cross-spawn@>=7.0.0 <7.0.5": ">=7.0.5"}}}