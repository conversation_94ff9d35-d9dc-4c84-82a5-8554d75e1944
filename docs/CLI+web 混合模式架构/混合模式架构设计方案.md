# LangGraph 智能体系统混合模式架构设计方案

## 📋 概述

本方案设计了一个混合模式架构，既保持现有的命令行版本，又添加可选的Web API模式，共享核心智能体逻辑，支持灵活的部署选择。

## 🎯 设计目标

1. **保持兼容性**：现有命令行版本完全不变
2. **共享核心逻辑**：智能体、工具、持久化逻辑复用
3. **灵活部署**：可选择命令行模式、Web API模式或混合模式
4. **渐进升级**：可以逐步从命令行迁移到Web API
5. **统一管理**：共享配置、数据库和会话管理

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                                │
├─────────────────────┬───────────────────────────────────────┤
│   命令行界面 (CLI)   │        Web API 界面                   │
│   - main.py         │   - FastAPI 服务器                    │
│   - 交互式对话       │   - RESTful API                      │
│   - 命令行工具       │   - WebSocket 实时通信                │
└─────────────────────┴───────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    服务抽象层                                │
│   - AgentService (核心智能体服务)                           │
│   - SessionService (会话管理服务)                           │
│   - ToolService (工具管理服务)                              │
│   - ConfigService (配置管理服务)                            │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    核心业务层                                │
│   - LangGraph 智能体 (现有逻辑)                             │
│   - MCP 工具系统 (现有逻辑)                                 │
│   - 持久化系统 (现有逻辑)                                   │
│   - 会话管理 (现有逻辑)                                     │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
│   - SQLite 数据库 (共享)                                    │
│   - 配置文件 (共享)                                         │
│   - 日志系统 (共享)                                         │
└─────────────────────────────────────────────────────────────┘
```

### 新的项目结构
```
my_project/
├── main.py                    # 现有命令行入口 (保持不变)
├── web_main.py                # 新增：Web API 入口
├── hybrid_main.py             # 新增：混合模式入口
├── 
├── core/                      # 新增：核心业务逻辑层
│   ├── __init__.py
│   ├── agent_core.py          # 从 main.py 提取的核心逻辑
│   ├── session_manager.py     # 增强的会话管理
│   ├── config_manager.py      # 统一配置管理
│   └── persistence.py         # 持久化抽象层
├── 
├── services/                  # 新增：服务抽象层
│   ├── __init__.py
│   ├── agent_service.py       # 智能体服务
│   ├── session_service.py     # 会话服务
│   ├── tool_service.py        # 工具服务
│   └── auth_service.py        # 认证服务 (Web API 用)
├── 
├── interfaces/                # 新增：接口层
│   ├── __init__.py
│   ├── cli/                   # 命令行接口
│   │   ├── __init__.py
│   │   └── cli_handler.py     # 从 main.py 提取的 CLI 逻辑
│   └── web/                   # Web API 接口
│       ├── __init__.py
│       ├── api/               # API 路由
│       │   ├── __init__.py
│       │   ├── auth.py
│       │   ├── sessions.py
│       │   ├── messages.py
│       │   └── system.py
│       ├── websocket/         # WebSocket 处理
│       │   ├── __init__.py
│       │   └── chat_handler.py
│       └── middleware/        # 中间件
│           ├── __init__.py
│           ├── auth.py
│           └── cors.py
├── 
├── config/                    # 配置文件目录
│   ├── app_config.json        # 新增：应用配置
│   ├── llm_config.json        # 现有：LLM 配置
│   ├── mcp_config.json        # 现有：MCP 配置
│   └── persistence_config.json # 现有：持久化配置
├── 
├── llm_loader.py              # 现有文件 (保持不变)
├── mcp_loader.py              # 现有文件 (保持不变)
├── data/                      # 现有数据目录 (共享)
├── docs/                      # 现有文档目录
└── tests/                     # 现有测试目录
```

## 🔧 核心组件设计

### 1. 应用配置 (config/app_config.json)
```json
{
  "app": {
    "name": "LangGraph智能体系统",
    "version": "2.0.0",
    "mode": "hybrid"
  },
  "interfaces": {
    "cli": {
      "enabled": true,
      "default_user": "cli_user"
    },
    "web": {
      "enabled": true,
      "host": "0.0.0.0",
      "port": 8000,
      "cors_origins": ["http://localhost:3000"],
      "auth_required": false
    }
  },
  "features": {
    "multi_agent": {
      "enabled": false,
      "supervisor_mode": true
    },
    "real_time_streaming": true,
    "session_isolation": true
  }
}
```

### 2. 核心智能体服务 (core/agent_core.py)
```python
# core/agent_core.py
"""
核心智能体逻辑 - 从 main.py 提取并增强
"""

import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.prebuilt import ToolNode

from llm_loader import load_llm_from_config
from mcp_loader import load_mcp_tools_from_config
from .persistence import create_checkpointer
from .config_manager import ConfigManager

class AgentCore:
    """核心智能体类 - 可被CLI和Web API共享使用"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.app = None
        self.tools = None
        self.checkpointer = None
        self._initialized = False
    
    async def initialize(self):
        """初始化智能体"""
        if self._initialized:
            return
        
        print("--- 初始化智能体核心 ---")
        
        # 创建检查点存储器
        self.checkpointer = await create_checkpointer(
            self.config_manager.get_persistence_config()
        )
        
        # 加载LLM和工具
        llm = load_llm_from_config("config/llm_config.json")
        _, self.tools = await load_mcp_tools_from_config("config/mcp_config.json")
        
        # 构建工作流
        self.app = await self._build_workflow(llm, self.tools)
        self._initialized = True
        
        print("--- 智能体核心初始化完成 ---")
    
    async def _build_workflow(self, llm, tools):
        """构建LangGraph工作流 - 与原main.py逻辑相同"""
        llm_with_tools = llm.bind_tools(tools)
        
        def call_model(state: MessagesState):
            messages = state["messages"]
            try:
                response = llm_with_tools.invoke(messages)
                if not response.content and not (hasattr(response, 'tool_calls') and response.tool_calls):
                    response = AIMessage(content="抱歉，我无法处理这个请求。请重新尝试。")
                return {"messages": [response]}
            except Exception as e:
                error_response = AIMessage(content=f"抱歉，处理请求时出现错误: {str(e)}")
                return {"messages": [error_response]}
        
        tool_node = ToolNode(tools)
        
        def should_continue(state: MessagesState):
            messages = state["messages"]
            last_message = messages[-1]
            if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "tools"
            return END
        
        # 构建图
        workflow = StateGraph(MessagesState)
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)
        workflow.set_entry_point("agent")
        workflow.add_conditional_edges("agent", should_continue, ["tools", END])
        workflow.add_edge("tools", "agent")
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    async def process_message(
        self, 
        message: str, 
        session_config: Dict[str, Any],
        stream: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理消息 - 支持流式和非流式"""
        if not self._initialized:
            await self.initialize()
        
        inputs = {"messages": [HumanMessage(content=message)]}
        
        if stream:
            async for output in self.app.astream(inputs, config=session_config, stream_mode="values"):
                yield {
                    "type": "stream_chunk",
                    "data": output,
                    "session_id": session_config["configurable"]["thread_id"]
                }
        else:
            result = await self.app.ainvoke(inputs, config=session_config)
            yield {
                "type": "final_result",
                "data": result,
                "session_id": session_config["configurable"]["thread_id"]
            }
    
    async def get_session_history(self, session_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取会话历史"""
        if not self._initialized:
            await self.initialize()
        
        try:
            state = await self.app.aget_state(session_config)
            if state.values and "messages" in state.values:
                return {
                    "success": True,
                    "messages": state.values["messages"],
                    "session_id": session_config["configurable"]["thread_id"]
                }
            else:
                return {
                    "success": True,
                    "messages": [],
                    "session_id": session_config["configurable"]["thread_id"]
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "session_id": session_config["configurable"]["thread_id"]
            }
    
    def get_available_tools(self):
        """获取可用工具列表"""
        if not self.tools:
            return []
        
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "parameters": getattr(tool, 'args_schema', None)
            }
            for tool in self.tools
        ]
```

### 3. 增强的会话管理 (core/session_manager.py)
```python
# core/session_manager.py
"""
增强的会话管理器 - 支持CLI和Web API
"""

import uuid
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class SessionInfo:
    """会话信息"""
    thread_id: str
    user_id: str
    created_at: datetime
    last_activity: datetime
    title: str = ""
    mode: str = "single_agent"
    status: str = "active"

class EnhancedSessionManager:
    """增强的会话管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        session_config = self.config.get("session_management", {})
        
        self.sessions: Dict[str, SessionInfo] = {}
        self.default_user_id = session_config.get("default_user_prefix", "default_user")
        self.session_timeout_hours = session_config.get("session_timeout_hours", 24)
        self.max_sessions_per_user = session_config.get("max_sessions_per_user", 10)
        self.auto_cleanup_enabled = session_config.get("auto_cleanup_enabled", True)
    
    def create_session(
        self, 
        user_id: Optional[str] = None, 
        title: str = "",
        mode: str = "single_agent"
    ) -> str:
        """创建新会话"""
        if user_id is None:
            user_id = self.default_user_id
        
        thread_id = f"{user_id}_{uuid.uuid4().hex[:8]}"
        now = datetime.now()
        
        session_info = SessionInfo(
            thread_id=thread_id,
            user_id=user_id,
            created_at=now,
            last_activity=now,
            title=title or f"会话 {now.strftime('%m-%d %H:%M')}",
            mode=mode,
            status="active"
        )
        
        self.sessions[thread_id] = session_info
        self._cleanup_old_sessions(user_id)
        
        return thread_id
    
    def get_session_config(self, thread_id: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取LangGraph标准的会话配置"""
        # 更新最后活动时间
        if thread_id in self.sessions:
            self.sessions[thread_id].last_activity = datetime.now()
        
        config = {"configurable": {"thread_id": thread_id}}
        
        if user_id:
            config["configurable"]["user_id"] = user_id
        elif thread_id in self.sessions:
            config["configurable"]["user_id"] = self.sessions[thread_id].user_id
        
        return config
    
    def get_user_sessions(self, user_id: str) -> List[SessionInfo]:
        """获取用户的所有会话"""
        return [
            session for session in self.sessions.values()
            if session.user_id == user_id and session.status == "active"
        ]
    
    def get_session_info(self, thread_id: str) -> Optional[SessionInfo]:
        """获取会话信息"""
        return self.sessions.get(thread_id)
    
    def update_session_title(self, thread_id: str, title: str) -> bool:
        """更新会话标题"""
        if thread_id in self.sessions:
            self.sessions[thread_id].title = title
            return True
        return False
    
    def delete_session(self, thread_id: str) -> bool:
        """删除会话"""
        if thread_id in self.sessions:
            self.sessions[thread_id].status = "deleted"
            return True
        return False
    
    def _cleanup_old_sessions(self, user_id: str):
        """清理旧会话"""
        if not self.auto_cleanup_enabled:
            return
        
        user_sessions = self.get_user_sessions(user_id)
        
        # 按创建时间排序，保留最新的会话
        user_sessions.sort(key=lambda x: x.created_at, reverse=True)
        
        # 删除超出限制的会话
        if len(user_sessions) > self.max_sessions_per_user:
            for session in user_sessions[self.max_sessions_per_user:]:
                session.status = "archived"
        
        # 删除超时的会话
        timeout_threshold = datetime.now() - timedelta(hours=self.session_timeout_hours)
        for session in user_sessions:
            if session.last_activity < timeout_threshold:
                session.status = "expired"
```

## 🚀 启动模式

### 1. 命令行模式 (保持现有)
```bash
# 使用现有的 main.py
uv run python main.py
```

### 2. Web API 模式
```bash
# 使用新的 web_main.py
uv run python web_main.py
```

### 3. 混合模式
```bash
# 使用新的 hybrid_main.py，同时启动CLI和Web API
uv run python hybrid_main.py
```

## 📈 实施计划

### Phase 1: 核心重构 (第1周)
- [ ] 提取核心逻辑到 `core/` 目录
- [ ] 创建服务抽象层
- [ ] 重构现有 `main.py` 使用新架构
- [ ] 确保向后兼容性

### Phase 2: Web API 实现 (第2-3周)
- [ ] 实现 FastAPI 服务器
- [ ] 创建 RESTful API 接口
- [ ] 实现 WebSocket 实时通信
- [ ] 添加基础认证系统

### Phase 3: 混合模式 (第4周)
- [ ] 实现混合启动模式
- [ ] 统一配置管理
- [ ] 完善文档和测试
- [ ] 性能优化

这个方案确保了现有功能的完全兼容，同时为Web API扩展提供了清晰的路径。您觉得这个设计方案如何？
