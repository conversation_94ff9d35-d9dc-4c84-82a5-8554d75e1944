"""
pytest配置文件
提供测试夹具和共享配置
"""
import pytest
import asyncio
import tempfile
import shutil
import os
from pathlib import Path
from typing import Generator, AsyncGenerator

# 设置测试环境变量
os.environ['TESTING'] = 'true'

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)

@pytest.fixture
def test_config_dir(temp_dir: Path) -> Path:
    """创建测试配置目录"""
    config_dir = temp_dir / "config"
    config_dir.mkdir(exist_ok=True)
    
    # 创建测试配置文件
    llm_config = {
        "provider": "zhipu",
        "model_name": "GLM-4-Flash",
        "api_base": "https://open.bigmodel.cn/api/paas/v4/",
        "api_key": "test_api_key_for_testing",
        "description": "智谱 GLM-4-Flash 模型"
    }
    
    mcp_config = {
        "servers": {
            "sequential-thinking": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
                "env": {}
            }
        }
    }
    
    persistence_config = {
        "type": "sqlite",
        "database_path": str(temp_dir / "test_memory.db")
    }
    
    import json
    with open(config_dir / "llm_config.json", "w") as f:
        json.dump(llm_config, f, indent=2)
    
    with open(config_dir / "mcp_config.json", "w") as f:
        json.dump(mcp_config, f, indent=2)
    
    with open(config_dir / "persistence_config.json", "w") as f:
        json.dump(persistence_config, f, indent=2)
    
    return config_dir

@pytest.fixture
async def test_agent(test_config_dir: Path):
    """创建测试用的智能体实例"""
    import sys
    sys.path.insert(0, str(Path(__file__).parent.parent))
    
    # 临时修改工作目录
    original_cwd = os.getcwd()
    os.chdir(test_config_dir.parent)
    
    try:
        import main
        agent, session_manager = main.initialize_agent()
        yield agent, session_manager
    finally:
        os.chdir(original_cwd)

@pytest.fixture
def mock_llm_response():
    """模拟LLM响应"""
    from langchain_core.messages import AIMessage
    return AIMessage(content="这是一个测试响应")

@pytest.fixture
def sample_tools():
    """创建示例工具用于测试"""
    from langchain_core.tools import tool
    
    @tool
    def test_tool(query: str) -> str:
        """测试工具"""
        return f"测试结果: {query}"
    
    return [test_tool]

@pytest.fixture
def performance_monitor():
    """性能监控fixture"""
    from tests.test_framework import TestFramework

    framework = TestFramework()
    return framework

class TestConfig:
    """测试配置类"""
    
    # 测试超时时间
    TIMEOUT = 30
    
    # 测试数据库路径
    TEST_DB_PATH = "test_memory.db"
    
    # 测试端口
    TEST_PORT = 8001
    
    # 测试主机
    TEST_HOST = "127.0.0.1"

# 测试标记
pytest_plugins = []

def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "cli: CLI相关测试"
    )
    config.addinivalue_line(
        "markers", "api: API相关测试"
    )
    config.addinivalue_line(
        "markers", "langgraph: LangGraph相关测试"
    )

def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为异步测试添加标记
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
