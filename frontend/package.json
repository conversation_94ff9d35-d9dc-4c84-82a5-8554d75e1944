{"name": "@chainlit/app", "private": true, "version": "2.4.1", "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint ./src --ext .ts,.tsx && tsc --noemit", "format": "prettier 'src/**/*.{ts,tsx,css}' --write", "test": "vitest run", "prepublishOnly": "pnpm run build && pnpm test"}, "dependencies": {"@chainlit/react-client": "workspace:^", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-table": "^8.20.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "embla-carousel-react": "^8.5.1", "highlight.js": "^11.9.0", "i18next": "^23.7.16", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "plotly.js": "^2.27.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-file-icon": "^1.3.0", "react-hook-form": "^7.54.2", "react-hotkeys-hook": "^4.4.1", "react-i18next": "^14.0.0", "react-markdown": "^9.0.1", "react-player": "^2.16.0", "react-plotly.js": "^2.6.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.15.0", "react-runner": "^1.0.5", "recoil": "^0.7.7", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "remark-directive": "^3.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sonner": "^1.2.3", "swr": "^2.2.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "unist-util-visit": "^5.0.0", "usehooks-ts": "^2.9.1", "uuid": "^9.0.0", "zod": "^3.24.1"}, "devDependencies": {"@swc/core": "^1.3.86", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@types/draft-js": "^0.11.10", "@types/lodash": "^4.14.199", "@types/node": "^20.5.7", "@types/react": "^18.3.1", "@types/react-file-icon": "^1.0.2", "@types/react-plotly.js": "^2.6.3", "@types/react-resizable": "^3.0.4", "@types/uuid": "^9.0.3", "@vitejs/plugin-react": "^4.0.4", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.20", "immutable": "^4.3.4", "jsdom": "^22.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^5.4.14", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.2.0", "vitest": "^0.34.4"}, "pnpm": {"overrides": {"vite@>=4.4.0 <4.4.12": ">=4.4.12", "vite@>=4.0.0 <=4.5.1": ">=4.5.2", "katex@>=0.11.0 <0.16.10": ">=0.16.10", "katex@>=0.15.4 <0.16.10": ">=0.16.10", "katex@>=0.10.0-beta <0.16.10": ">=0.16.10", "vite@>=4.0.0 <=4.5.2": ">=4.5.3", "braces@<3.0.3": ">=3.0.3", "ws@>=8.0.0 <8.17.1": ">=8.17.1", "micromatch@<4.0.8": ">=4.0.8", "vite@>=4.0.0 <4.5.4": ">=4.5.4", "vite@>=4.0.0 <=4.5.3": ">=4.5.4", "rollup@>=3.0.0 <3.29.5": ">=3.29.5", "rollup@>=4.0.0 <4.22.4": ">=4.22.4", "cross-spawn@>=7.0.0 <7.0.5": ">=7.0.5"}}}