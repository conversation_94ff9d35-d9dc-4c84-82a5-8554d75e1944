# LangGraph 智能体系统前端API开发文档

## 📋 文档概述

本文档专为前端开发小白设计，提供了完整的API接口说明、示例代码和最佳实践，帮助您快速开发智能体系统的前端界面。

## 🎯 快速开始

### 系统架构
```
前端界面 ↔ FastAPI后端 ↔ LangGraph智能体系统
```

### 基础信息
- **API基础URL**: `http://localhost:8000/api`
- **WebSocket URL**: `ws://localhost:8000/ws`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 环境要求
- Node.js 16+
- 现代浏览器（支持WebSocket）
- 网络连接

## 🔐 认证系统

### 1. 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "user123",
  "password": "password123"
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": "user123",
      "username": "user123",
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

**JavaScript示例**：
```javascript
async function login(username, password) {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    
    if (data.success) {
      // 保存token到localStorage
      localStorage.setItem('access_token', data.data.access_token);
      return data.data.user;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}
```

### 2. 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer {access_token}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "id": "user123",
    "username": "user123",
    "sessions_count": 5,
    "last_login": "2024-01-01T12:00:00Z"
  }
}
```

### 3. 用户登出
```http
POST /api/auth/logout
Authorization: Bearer {access_token}
```

## 💬 会话管理

### 1. 获取会话列表
```http
GET /api/sessions
Authorization: Bearer {access_token}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "session_123",
        "title": "AI发展趋势讨论",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T11:30:00Z",
        "message_count": 8,
        "status": "active"
      }
    ],
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}
```

**React组件示例**：
```jsx
import React, { useState, useEffect } from 'react';

function SessionList() {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSessions();
  }, []);

  const fetchSessions = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/sessions', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setSessions(data.data.sessions);
      }
    } catch (error) {
      console.error('获取会话列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="session-list">
      {sessions.map(session => (
        <div key={session.id} className="session-item">
          <h3>{session.title}</h3>
          <p>消息数: {session.message_count}</p>
          <p>更新时间: {new Date(session.updated_at).toLocaleString()}</p>
        </div>
      ))}
    </div>
  );
}
```

### 2. 创建新会话
```http
POST /api/sessions
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "title": "新的对话",
  "mode": "multi_agent"
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "session": {
      "id": "session_456",
      "title": "新的对话",
      "mode": "multi_agent",
      "created_at": "2024-01-01T12:00:00Z",
      "status": "active"
    }
  }
}
```

### 3. 获取会话详情
```http
GET /api/sessions/{session_id}
Authorization: Bearer {access_token}
```

### 4. 删除会话
```http
DELETE /api/sessions/{session_id}
Authorization: Bearer {access_token}
```

## 📨 消息处理

### 1. 发送消息
```http
POST /api/sessions/{session_id}/messages
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "content": "请帮我分析一下人工智能的发展趋势",
  "type": "text"
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "message_id": "msg_789",
    "session_id": "session_123",
    "status": "processing"
  }
}
```

### 2. 获取消息历史
```http
GET /api/sessions/{session_id}/messages?page=1&per_page=20
Authorization: Bearer {access_token}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": "msg_001",
        "role": "user",
        "content": "请帮我分析一下人工智能的发展趋势",
        "timestamp": "2024-01-01T10:00:00Z"
      },
      {
        "id": "msg_002",
        "role": "assistant",
        "content": "我来为您分析人工智能的发展趋势...",
        "timestamp": "2024-01-01T10:01:00Z",
        "agent_info": {
          "agent_name": "search_specialist",
          "tools_used": ["tavily_search", "web-search"]
        }
      }
    ],
    "total": 2,
    "page": 1,
    "per_page": 20
  }
}
```

## 🔄 实时通信（WebSocket）

### 连接WebSocket
```javascript
class ChatWebSocket {
  constructor(sessionId, token) {
    this.sessionId = sessionId;
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `ws://localhost:8000/ws/sessions/${this.sessionId}?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case 'message_chunk':
        // 处理流式消息块
        this.onMessageChunk(data.content);
        break;
      case 'agent_status':
        // 处理智能体状态更新
        this.onAgentStatus(data.agent, data.status);
        break;
      case 'message_complete':
        // 消息完成
        this.onMessageComplete(data.message);
        break;
      case 'error':
        // 错误处理
        this.onError(data.error);
        break;
    }
  }

  sendMessage(content) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'user_message',
        content: content
      }));
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}
```

### WebSocket消息类型

#### 1. 流式消息块
```json
{
  "type": "message_chunk",
  "message_id": "msg_123",
  "content": "人工智能的发展趋势主要体现在",
  "is_final": false
}
```

#### 2. 智能体状态更新
```json
{
  "type": "agent_status",
  "agent": "search_specialist",
  "status": "working",
  "message": "正在搜索最新的AI发展信息..."
}
```

#### 3. 消息完成
```json
{
  "type": "message_complete",
  "message": {
    "id": "msg_123",
    "role": "assistant",
    "content": "完整的回复内容...",
    "agent_info": {
      "agent_name": "search_specialist",
      "execution_time": 2.5,
      "tools_used": ["tavily_search"]
    }
  }
}
```

## 🤖 多智能体状态

### 获取智能体状态
```http
GET /api/system/agents
Authorization: Bearer {access_token}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "name": "supervisor",
        "status": "active",
        "description": "任务协调和智能体管理",
        "current_task": null
      },
      {
        "name": "search_specialist",
        "status": "working",
        "description": "信息搜索和检索专家",
        "current_task": "搜索AI发展趋势",
        "progress": 75
      },
      {
        "name": "analysis_specialist",
        "status": "idle",
        "description": "逻辑推理和数据分析专家",
        "current_task": null
      }
    ],
    "total_agents": 6,
    "active_agents": 2
  }
}
```

### React智能体状态组件
```jsx
import React, { useState, useEffect } from 'react';

function AgentStatus() {
  const [agents, setAgents] = useState([]);

  useEffect(() => {
    const fetchAgentStatus = async () => {
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch('/api/system/agents', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        const data = await response.json();
        if (data.success) {
          setAgents(data.data.agents);
        }
      } catch (error) {
        console.error('获取智能体状态失败:', error);
      }
    };

    fetchAgentStatus();
    const interval = setInterval(fetchAgentStatus, 2000); // 每2秒更新

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4CAF50';
      case 'working': return '#FF9800';
      case 'idle': return '#9E9E9E';
      case 'error': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  return (
    <div className="agent-status">
      <h3>智能体状态</h3>
      {agents.map(agent => (
        <div key={agent.name} className="agent-item">
          <div className="agent-header">
            <span 
              className="status-indicator"
              style={{ backgroundColor: getStatusColor(agent.status) }}
            />
            <h4>{agent.name}</h4>
            <span className="status-text">{agent.status}</span>
          </div>
          <p className="agent-description">{agent.description}</p>
          {agent.current_task && (
            <div className="current-task">
              <p>当前任务: {agent.current_task}</p>
              {agent.progress && (
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${agent.progress}%` }}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
```

## 📁 文件管理

### 1. 文件上传
```http
POST /api/files/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

FormData:
- file: [文件内容]
- session_id: "session_123"
- description: "用户上传的文档"
```

**JavaScript上传示例**：
```javascript
async function uploadFile(file, sessionId, description = '') {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('session_id', sessionId);
  formData.append('description', description);

  try {
    const token = localStorage.getItem('access_token');
    const response = await fetch('/api/files/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    const data = await response.json();
    if (data.success) {
      return data.data.file;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
}
```

### 2. 文件下载
```http
GET /api/files/{file_id}
Authorization: Bearer {access_token}
```

## ⚠️ 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "username",
      "reason": "用户名不能为空"
    }
  }
}
```

### 常见错误代码
- `AUTHENTICATION_REQUIRED` (401): 需要认证
- `PERMISSION_DENIED` (403): 权限不足
- `RESOURCE_NOT_FOUND` (404): 资源不存在
- `VALIDATION_ERROR` (422): 参数验证失败
- `RATE_LIMIT_EXCEEDED` (429): 请求频率超限
- `INTERNAL_SERVER_ERROR` (500): 服务器内部错误

### 错误处理最佳实践
```javascript
class ApiClient {
  constructor() {
    this.baseURL = '/api';
    this.token = localStorage.getItem('access_token');
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(data.error, response.status);
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError({
        code: 'NETWORK_ERROR',
        message: '网络请求失败'
      }, 0);
    }
  }
}

class ApiError extends Error {
  constructor(error, status) {
    super(error.message);
    this.code = error.code;
    this.status = status;
    this.details = error.details;
  }
}

// 使用示例
const apiClient = new ApiClient();

try {
  const sessions = await apiClient.request('/sessions');
  console.log('会话列表:', sessions.data.sessions);
} catch (error) {
  if (error instanceof ApiError) {
    switch (error.code) {
      case 'AUTHENTICATION_REQUIRED':
        // 跳转到登录页面
        window.location.href = '/login';
        break;
      case 'RATE_LIMIT_EXCEEDED':
        // 显示限流提示
        alert('请求过于频繁，请稍后再试');
        break;
      default:
        // 显示通用错误信息
        alert(`错误: ${error.message}`);
    }
  }
}
```

## 🎨 完整的聊天界面示例

### React聊天组件
```jsx
import React, { useState, useEffect, useRef } from 'react';

function ChatInterface({ sessionId }) {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [agentStatus, setAgentStatus] = useState({});
  const wsRef = useRef(null);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    // 初始化WebSocket连接
    initWebSocket();
    // 加载历史消息
    loadMessageHistory();

    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, [sessionId]);

  useEffect(() => {
    // 自动滚动到底部
    scrollToBottom();
  }, [messages]);

  const initWebSocket = () => {
    const token = localStorage.getItem('access_token');
    wsRef.current = new ChatWebSocket(sessionId, token);

    wsRef.current.onMessageChunk = (content) => {
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
          return [
            ...prev.slice(0, -1),
            { ...lastMessage, content: lastMessage.content + content }
          ];
        } else {
          return [
            ...prev,
            {
              id: `temp_${Date.now()}`,
              role: 'assistant',
              content: content,
              isStreaming: true,
              timestamp: new Date().toISOString()
            }
          ];
        }
      });
    };

    wsRef.current.onMessageComplete = (message) => {
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isStreaming);
        return [...filtered, message];
      });
      setIsLoading(false);
    };

    wsRef.current.onAgentStatus = (agent, status) => {
      setAgentStatus(prev => ({
        ...prev,
        [agent]: status
      }));
    };

    wsRef.current.onError = (error) => {
      console.error('WebSocket错误:', error);
      setIsLoading(false);
    };

    wsRef.current.connect();
  };

  const loadMessageHistory = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`/api/sessions/${sessionId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setMessages(data.data.messages);
      }
    } catch (error) {
      console.error('加载消息历史失败:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 通过WebSocket发送消息
    if (wsRef.current) {
      wsRef.current.sendMessage(inputValue);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="chat-interface">
      {/* 智能体状态栏 */}
      <div className="agent-status-bar">
        {Object.entries(agentStatus).map(([agent, status]) => (
          <div key={agent} className={`agent-indicator ${status}`}>
            <span className="agent-name">{agent}</span>
            <span className="agent-status">{status}</span>
          </div>
        ))}
      </div>

      {/* 消息列表 */}
      <div className="messages-container">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="message-header">
              <span className="role">{message.role === 'user' ? '👤' : '🤖'}</span>
              <span className="timestamp">
                {new Date(message.timestamp).toLocaleTimeString()}
              </span>
              {message.agent_info && (
                <span className="agent-info">
                  {message.agent_info.agent_name}
                </span>
              )}
            </div>
            <div className="message-content">
              {message.content}
              {message.isStreaming && <span className="cursor">|</span>}
            </div>
            {message.agent_info && message.agent_info.tools_used && (
              <div className="tools-used">
                使用工具: {message.agent_info.tools_used.join(', ')}
              </div>
            )}
          </div>
        ))}
        {isLoading && (
          <div className="loading-indicator">
            <div className="typing-animation">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span>AI正在思考中...</span>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="input-container">
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="输入您的问题..."
          disabled={isLoading}
          rows={3}
        />
        <button
          onClick={sendMessage}
          disabled={!inputValue.trim() || isLoading}
          className="send-button"
        >
          {isLoading ? '发送中...' : '发送'}
        </button>
      </div>
    </div>
  );
}
```

### CSS样式示例
```css
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.agent-status-bar {
  display: flex;
  gap: 10px;
  padding: 10px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  overflow-x: auto;
}

.agent-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  white-space: nowrap;
}

.agent-indicator.active { background: #e8f5e8; color: #2e7d32; }
.agent-indicator.working { background: #fff3e0; color: #f57c00; }
.agent-indicator.idle { background: #f5f5f5; color: #757575; }

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fafafa;
}

.message {
  margin-bottom: 20px;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
}

.message.assistant {
  margin-right: auto;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.user .message-content {
  background: #007bff;
  color: white;
}

.message.assistant .message-content {
  background: white;
  border: 1px solid #ddd;
  color: #333;
}

.cursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.tools-used {
  font-size: 11px;
  color: #888;
  margin-top: 5px;
  font-style: italic;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  color: #666;
}

.typing-animation {
  display: flex;
  gap: 3px;
}

.typing-animation span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) { animation-delay: -0.32s; }
.typing-animation span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.input-container {
  display: flex;
  gap: 10px;
  padding: 20px;
  background: white;
  border-top: 1px solid #ddd;
}

.input-container textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
}

.send-button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.send-button:hover:not(:disabled) {
  background: #0056b3;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
```

## 📱 响应式设计建议

### 移动端适配
```css
@media (max-width: 768px) {
  .chat-interface {
    height: 100vh;
    border-radius: 0;
    border: none;
  }

  .agent-status-bar {
    padding: 8px;
    font-size: 11px;
  }

  .messages-container {
    padding: 15px;
  }

  .message {
    max-width: 90%;
  }

  .input-container {
    padding: 15px;
  }

  .input-container textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
```

## 🔧 开发工具和调试

### 1. Postman集合
创建一个Postman集合来测试API：

```json
{
  "info": {
    "name": "LangGraph智能体API",
    "description": "智能体系统API测试集合"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8000/api"
    },
    {
      "key": "token",
      "value": "{{access_token}}"
    }
  ],
  "item": [
    {
      "name": "认证",
      "item": [
        {
          "name": "登录",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"username\": \"test_user\",\n  \"password\": \"test_password\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/auth/login",
              "host": ["{{baseUrl}}"],
              "path": ["auth", "login"]
            }
          }
        }
      ]
    }
  ]
}
```

### 2. 开发环境配置
```javascript
// config/development.js
export const config = {
  API_BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
  WS_BASE_URL: process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws',
  DEBUG: process.env.NODE_ENV === 'development',
  TOKEN_STORAGE_KEY: 'langgraph_access_token'
};

// utils/logger.js
export const logger = {
  debug: (message, data) => {
    if (config.DEBUG) {
      console.log(`[DEBUG] ${message}`, data);
    }
  },
  error: (message, error) => {
    console.error(`[ERROR] ${message}`, error);
  },
  info: (message, data) => {
    console.info(`[INFO] ${message}`, data);
  }
};
```

### 3. 状态管理建议（Redux Toolkit）
```javascript
// store/chatSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ sessionId, content }, { rejectWithValue }) => {
    try {
      const response = await apiClient.request(`/sessions/${sessionId}/messages`, {
        method: 'POST',
        body: JSON.stringify({ content, type: 'text' })
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState: {
    sessions: [],
    currentSession: null,
    messages: [],
    isLoading: false,
    error: null,
    agentStatus: {}
  },
  reducers: {
    setCurrentSession: (state, action) => {
      state.currentSession = action.payload;
    },
    addMessage: (state, action) => {
      state.messages.push(action.payload);
    },
    updateAgentStatus: (state, action) => {
      const { agent, status } = action.payload;
      state.agentStatus[agent] = status;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isLoading = false;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  }
});

export const { setCurrentSession, addMessage, updateAgentStatus, clearError } = chatSlice.actions;
export default chatSlice.reducer;
```
```
