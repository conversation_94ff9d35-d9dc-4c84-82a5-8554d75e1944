#!/usr/bin/env python3
"""
CLI对话功能测试 - 验证LangGraph官方标准实现
"""

import asyncio
import sys
import os
from langchain_core.messages import HumanMessage

async def test_conversation():
    try:
        print('🧪 测试CLI对话功能...')
        
        # 导入主模块
        import main
        
        # 初始化Agent
        app, tools, session_manager = await main.initialize_agent()
        print(f'✅ Agent初始化成功，工具数量: {len(tools)}')
        
        # 创建测试会话
        thread_id = session_manager.create_session('test_user')
        config = session_manager.get_session_config(thread_id)
        print(f'✅ 测试会话创建: {thread_id}')
        
        # 测试基本对话
        test_message = "你好，请简单介绍一下你自己"
        print(f'📤 发送测试消息: {test_message}')
        
        # 获取初始状态
        initial_state = await app.aget_state(config)
        print(f'✅ 获取初始状态成功')
        
        # 添加用户消息
        await app.aupdate_state(config, {"messages": [HumanMessage(content=test_message)]})
        print(f'✅ 用户消息已添加到状态')
        
        # 运行Agent
        print('🤖 运行Agent处理消息...')
        result = await app.ainvoke(None, config)
        print(f'✅ Agent处理完成')
        
        # 获取最终状态
        final_state = await app.aget_state(config)
        if final_state.values and "messages" in final_state.values:
            messages = final_state.values["messages"]
            print(f'✅ 对话历史包含 {len(messages)} 条消息')
            
            # 显示最后的AI回复
            for msg in messages:
                if hasattr(msg, 'content') and msg.content:
                    msg_type = "👤" if isinstance(msg, HumanMessage) else "🤖"
                    print(f'{msg_type} {msg.content[:100]}...')
        else:
            print('❌ 无法获取对话历史')
            return False
        
        # 测试会话持久化
        print('\n🔄 测试会话持久化...')
        
        # 发送第二条消息
        second_message = "你有哪些功能？"
        await app.aupdate_state(config, {"messages": [HumanMessage(content=second_message)]})
        await app.ainvoke(None, config)
        
        # 验证持久化
        persistent_state = await app.aget_state(config)
        if persistent_state.values and "messages" in persistent_state.values:
            persistent_messages = persistent_state.values["messages"]
            print(f'✅ 持久化验证成功，总消息数: {len(persistent_messages)}')
        else:
            print('❌ 持久化验证失败')
            return False
        
        # 测试会话恢复
        print('\n🔄 测试会话恢复...')
        
        # 创建新的会话管理器实例（模拟重启）
        new_session_manager = main.SimpleSessionManager()
        resumed_thread_id = new_session_manager.resume_session(thread_id)
        resumed_config = new_session_manager.get_session_config(resumed_thread_id)
        
        # 验证恢复的会话状态
        resumed_state = await app.aget_state(resumed_config)
        if resumed_state.values and "messages" in resumed_state.values:
            resumed_messages = resumed_state.values["messages"]
            print(f'✅ 会话恢复成功，恢复消息数: {len(resumed_messages)}')
        else:
            print('❌ 会话恢复失败')
            return False
        
        print('\n🎉 CLI对话功能测试完全通过！')
        print('✅ 基本对话功能正常')
        print('✅ 状态持久化正常')
        print('✅ 会话恢复正常')
        print('✅ LangGraph官方标准实现正确')
        
        return True
        
    except Exception as e:
        print(f'❌ CLI对话测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

async def test_tools_integration():
    """测试工具集成"""
    try:
        print('\n🛠️ 测试工具集成...')
        
        import main
        
        # 初始化Agent
        app, tools, session_manager = await main.initialize_agent()
        
        # 验证工具数量
        expected_tools = 35  # 根据输出确认的工具数量
        if len(tools) == expected_tools:
            print(f'✅ 工具数量正确: {len(tools)}')
        else:
            print(f'⚠️ 工具数量异常: 期望{expected_tools}，实际{len(tools)}')
        
        # 验证关键工具存在
        tool_names = [tool.name for tool in tools]
        key_tools = ['sequentialthinking', 'tavily-search', 'excel_read_sheet']
        
        for tool_name in key_tools:
            if tool_name in tool_names:
                print(f'✅ 关键工具存在: {tool_name}')
            else:
                print(f'❌ 关键工具缺失: {tool_name}')
                return False
        
        print('✅ 工具集成测试通过')
        return True
        
    except Exception as e:
        print(f'❌ 工具集成测试失败: {e}')
        return False

async def main():
    """主测试函数"""
    print('🚀 开始CLI功能完整性测试')
    print('严格验证LangGraph官方标准合规性')
    print('='*60)
    
    # 运行对话功能测试
    conversation_result = await test_conversation()
    
    # 运行工具集成测试
    tools_result = await test_tools_integration()
    
    # 总结测试结果
    print('\n' + '='*60)
    print('📊 测试结果总结:')
    print(f'✅ 对话功能测试: {"通过" if conversation_result else "失败"}')
    print(f'✅ 工具集成测试: {"通过" if tools_result else "失败"}')
    
    overall_success = conversation_result and tools_result
    
    if overall_success:
        print('\n🎉 所有CLI功能测试通过！')
        print('✅ 原有功能完全正常，无破坏性变更')
        print('✅ 严格遵循LangGraph官方标准')
        print('✅ AsyncSqliteSaver持久化正常工作')
        print('✅ 35个MCP工具正常加载')
        return 0
    else:
        print('\n⚠️ 部分测试失败，需要修复')
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
