{"name": "@chainlit/react-client", "description": "Websocket client to connect to your chainlit app.", "version": "0.2.4", "scripts": {"build": "tsup src/index.ts --tsconfig tsconfig.build.json --clean --format esm,cjs --dts  --external react --external recoil --minify --sourcemap --treeshake", "dev": "tsup src/index.ts --clean --format esm,cjs --dts  --external react --external recoil --minify --sourcemap --treeshake", "lint": "eslint ./src --ext ts,tsx --report-unused-disable-directives --max-warnings 0 && tsc --noemit", "format": "prettier '**/*.{ts,tsx}' --write", "test": "echo no tests yet", "prepublishOnly": "pnpm run build"}, "repository": {"type": "git", "url": "https://github.com/Chainlit/"}, "private": false, "keywords": ["llm", "ai", "chain of thought"], "author": "Chainlit", "license": "Apache-2.0", "files": ["dist", "README.md"], "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "devDependencies": {"@swc/core": "^1.3.86", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@types/lodash": "^4.14.199", "@types/uuid": "^9.0.3", "@vitejs/plugin-react": "^4.0.4", "@vitejs/plugin-react-swc": "^3.3.2", "jsdom": "^22.1.0", "tslib": "^2.6.2", "tsup": "^7.2.0", "typescript": "^5.2.2", "vite": "^5.4.14", "vite-tsconfig-paths": "^4.2.0", "vitest": "^0.34.4"}, "peerDependencies": {"@types/react": "^18.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "recoil": "^0.7.7"}, "dependencies": {"jwt-decode": "^3.1.2", "lodash": "^4.17.21", "socket.io-client": "^4.7.2", "sonner": "^1.7.1", "swr": "^2.2.2", "uuid": "^9.0.0"}, "pnpm": {"overrides": {"vite@>=4.4.0 <4.4.12": ">=4.4.12", "@adobe/css-tools@<4.3.2": ">=4.3.2", "vite@>=4.0.0 <=4.5.1": ">=4.5.2", "vite@>=4.0.0 <=4.5.2": ">=4.5.3", "braces@<3.0.3": ">=3.0.3", "ws@>=8.0.0 <8.17.1": ">=8.17.1", "micromatch@<4.0.8": ">=4.0.8", "vite@>=4.0.0 <4.5.4": ">=4.5.4", "vite@>=4.0.0 <=4.5.3": ">=4.5.4", "rollup@>=3.0.0 <3.29.5": ">=3.29.5", "cross-spawn@>=7.0.0 <7.0.5": ">=7.0.5"}}}