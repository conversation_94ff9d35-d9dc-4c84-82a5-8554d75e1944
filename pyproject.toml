[project]
name = "my-project"
version = "0.1.0"
description = "基于LangChain和LangGraph构建的智能代理系统，集成了MCP工具"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langchain>=0.3.0",
    "langgraph[sqlite]>=0.2.0",
    "langchain-openai>=0.2.0",
    "langchain-mcp-adapters>=0.1.0",
    "langchain-core>=0.3.0",
    "aiosqlite>=0.19.0",
    "langgraph-checkpoint-sqlite>=2.0.10",
]

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"
allow-insecure-host = ["mirrors.aliyun.com"]
