{"tests/unit/test_core_components.py::TestConfigManager::test_load_llm_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_load_mcp_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_load_persistence_config": true, "tests/unit/test_core_components.py::TestConfigManager::test_invalid_config_path": true, "tests/unit/test_core_components.py::TestSessionManager::test_create_session": true, "tests/unit/test_core_components.py::TestSessionManager::test_get_session_config": true, "tests/unit/test_core_components.py::TestSessionManager::test_list_sessions": true, "tests/unit/test_core_components.py::TestSessionManager::test_session_cleanup": true, "tests/unit/test_core_components.py::TestAgentCore::test_agent_initialization": true, "tests/unit/test_core_components.py::TestAgentCore::test_create_workflow": true, "tests/unit/test_core_components.py::TestAgentCore::test_process_message": true, "tests/unit/test_core_components.py::TestUtilities::test_generate_session_id": true, "tests/unit/test_core_components.py::TestUtilities::test_validate_config": true, "tests/unit/test_core_components.py::TestPerformance::test_config_loading_performance": true, "tests/unit/test_core_components.py::TestPerformance::test_session_creation_performance": true, "tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_complete_cli_conversation_workflow": true, "tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_multi_session_workflow": true, "tests/e2e/test_complete_workflows.py::TestCLIWorkflows::test_session_persistence_across_restarts": true, "tests/e2e/test_complete_workflows.py::TestWebAPIWorkflows::test_websocket_workflow": true, "tests/e2e/test_complete_workflows.py::TestPerformanceWorkflows::test_high_load_workflow": true, "tests/e2e/test_complete_workflows.py::TestPerformanceWorkflows::test_memory_usage_workflow": true, "tests/e2e/test_complete_workflows.py::TestRobustnessWorkflows::test_error_recovery_workflow": true}