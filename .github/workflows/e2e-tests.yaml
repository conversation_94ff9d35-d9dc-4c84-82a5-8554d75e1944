name: E2ETests

on: [workflow_call]

permissions: read-all

jobs:
  ci:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest]
    env:
      BACKEND_DIR: ./backend
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/pnpm-node-install
        name: Install Node, pnpm and dependencies.
        with:
          pnpm-skip-install: true
      - name: Install depdendencies and Cypress
        uses: cypress-io/github-action@v6
        with:
          runTests: false
      - uses: ./.github/actions/poetry-python-install
        name: Install Python, poetry and Python dependencies
        with:
          poetry-working-directory: ${{ env.BACKEND_DIR }}
          poetry-install-args: --with tests
      - name: Run tests
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 20
          max_attempts: 3
          command: pnpm test
