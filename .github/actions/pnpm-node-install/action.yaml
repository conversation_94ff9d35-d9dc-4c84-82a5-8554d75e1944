name: Install Node, pnpm and dependencies.
description: Install Node, pnpm and dependencies using cache.

inputs:
  node-version:
    description: Node.js version
    required: true
    default: '23.3.0'
  pnpm-version:
    description: pnpm version
    required: true
    default: '9.7.0'
  pnpm-skip-install:
    description: Skip install.
    required: false
    default: 'false'
  pnpm-install-args:
    description: Extra arguments for pnpm install, e.g. --no-frozen-lockfile.
    default: '--frozen-lockfile'

runs:
  using: composite
  steps:
    - uses: pnpm/action-setup@v4
      with:
        version: ${{ inputs.pnpm-version }}
    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: pnpm
    - name: Install JS dependencies
      run: pnpm install ${{ inputs.pnpm-install-args }}
      shell: bash
      # Skip install if pnpm-skip-install is true
      if: ${{ inputs.pnpm-skip-install != 'true' }}
